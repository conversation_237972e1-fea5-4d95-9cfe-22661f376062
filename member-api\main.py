from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging
import time
from contextlib import asynccontextmanager

from core.config import settings
from core.database import db
from core.exception_handlers import setup_exception_handlers
from utils.setup_logging import setup_logging
from api.query.router import router as query_router
from api.PPTreport.router import router as ppt_report_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时
    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("应用启动中...")
    
    # 初始化数据库连接
    await db.connect()
    logger.info("数据库连接已建立")
    
    yield
    
    # 关闭时
    await db.disconnect()
    logger.info("应用已关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title="会员报表API",
    description="会员数据分析报表系统API",
    version="1.0.0",
    lifespan=lifespan
)

# 设置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册异常处理器
setup_exception_handlers(app)

# 注册路由
app.include_router(query_router, prefix="/api/query", tags=["查询"])
app.include_router(ppt_report_router, prefix="/api/ppt-report", tags=["PPT报告"])

# 文件下载路由已经包含在ppt_report_router中，路径为 /api/ppt-report/download/{folder}/{filename}
# 不需要单独的文件路由

@app.middleware("http")
async def log_requests(request: Request, call_next):
    """请求日志中间件"""
    start_time = time.time()
    logger = logging.getLogger("request")
    
    # 记录请求信息
    logger.info(f"收到请求: {request.method} {request.url}")
    logger.info(f"请求头: {dict(request.headers)}")
    
    # 处理请求
    response = await call_next(request)
    
    # 记录响应信息
    process_time = time.time() - start_time
    logger.info(f"请求处理完成: {request.method} {request.url} - 状态码: {response.status_code} - 耗时: {process_time:.3f}秒")
    
    return response

@app.get("/")
async def root():
    """根路径健康检查"""
    return {"message": "会员报表API服务运行正常", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "environment": settings.ENVIRONMENT}

@app.get("/test")
async def test_endpoint():
    """测试端点"""
    return {"message": "测试端点正常", "timestamp": time.time()}

@app.post("/test-post")
async def test_post_endpoint(request: Request):
    """测试POST端点"""
    try:
        body = await request.json()
        return {"message": "POST测试成功", "received_data": body}
    except Exception as e:
        return {"message": "POST测试失败", "error": str(e)}


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.API_RELOAD,
        log_level=settings.LOG_LEVEL.lower()
    )
