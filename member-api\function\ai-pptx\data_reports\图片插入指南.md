# 📸 PPT模板图片插入指南

## 🎯 如何在PPT模板中插入图片

### 步骤1：在PPT模板中创建图片占位符

1. **打开PPT模板文件**：`ppt_template/会员数据报告 -模板.pptx`

2. **创建文本框**：
   - 在需要插入图片的位置创建一个文本框
   - 调整文本框的大小和位置（这将决定最终图片的大小和位置）

3. **输入图片参数**：
   - 在文本框中输入图片参数，格式：`{image_参数名}`
   - 例如：`{image_test}` 对应您的 test.PNG 图片

### 步骤2：配置图片路径

在 `data_reports/constants.py` 文件中，图片已经配置好了：

```python
# 您的test.PNG图片配置
TEST_IMAGE = "./data_reports/images/test.PNG"

# 在数据字典中的映射
MEMBER_DATA = {
    # ... 其他参数 ...
    "image_test": TEST_IMAGE,  # 对应 {image_test} 参数
}
```

### 步骤3：运行生成脚本

```bash
python data_reports/generate_member_report.py
```

### 📋 当前可用的图片参数

| 参数名 | 对应文件 | 用途说明 |
|--------|----------|----------|
| `{image_logo}` | company_logo.png | 公司Logo |
| `{image_chart}` | data_chart.png | 数据图表 |
| `{image_trend}` | member_trend.png | 会员趋势图 |
| `{image_revenue}` | revenue_chart.png | 收入图表 |
| `{image_coupon}` | coupon_analysis.png | 优惠券分析图 |
| `{image_test}` | test.PNG | 您的测试图片 |

### 🔧 添加新图片的方法

如果您想添加更多图片：

1. **将图片文件放到** `data_reports/images/` 目录下

2. **在 constants.py 中添加配置**：
```python
# 添加新图片路径
NEW_IMAGE = "./data_reports/images/your_new_image.png"

# 在数据字典中添加映射
MEMBER_DATA = {
    # ... 现有参数 ...
    "image_new": NEW_IMAGE,  # 新增图片参数
}
```

3. **在PPT模板中使用** `{image_new}` 作为占位符

### ⚠️ 注意事项

1. **参数命名规则**：
   - 图片参数必须以 `image_` 开头
   - 例如：`image_logo`、`image_chart`、`image_test`

2. **文件格式支持**：
   - PNG（推荐，支持透明背景）
   - JPG/JPEG
   - GIF
   - BMP

3. **文件路径**：
   - 确保图片文件存在于指定路径
   - 路径区分大小写（test.PNG vs test.png）

4. **图片尺寸**：
   - 图片会按照文本框的大小进行缩放
   - 建议使用高质量图片以保证缩放效果

### 🚀 快速测试

1. **在PPT模板中添加文本框**，输入：`{image_test}`

2. **确保test.PNG文件存在**：`data_reports/images/test.PNG`

3. **运行生成脚本**：
```bash
python data_reports/generate_member_report.py
```

4. **查看生成的PPT**，文本框应该被替换为您的test.PNG图片

### 📊 生成日志示例

成功插入图片时，您会看到类似的日志：

```
INFO - 幻灯片X: 图片参数 {image_test} -> ./data_reports/images/test.PNG
INFO - 成功插入图片: ./data_reports/images/test.PNG
```

如果图片文件不存在，会显示警告：

```
WARNING - 图片文件不存在: ./data_reports/images/test.PNG
```

### 🎨 图片优化建议

- **分辨率**：建议使用适中的分辨率（如1920x1080以内）
- **文件大小**：控制在5MB以内以保证PPT文件大小合理
- **格式选择**：PNG格式适合图标和透明背景，JPG适合照片
- **命名规范**：使用有意义的文件名，便于管理
