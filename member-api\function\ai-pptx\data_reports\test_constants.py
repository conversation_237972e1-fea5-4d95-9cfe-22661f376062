#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试 constants.py 文件
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from data_reports.constants import (
        MEMBER_DATA, 
        print_data_summary, 
        print_validation_report,
        TOTAL_MEMBERS,
        NEW_MEMBERS
    )
    
    print("✅ constants.py 导入成功!")
    print(f"📊 参数总数: {len(MEMBER_DATA)}")
    print(f"👥 会员总量: {TOTAL_MEMBERS}")
    print(f"🆕 新增会员: {NEW_MEMBERS}")
    
    print("\n" + "="*50)
    print("📋 所有可用参数:")
    print("="*50)
    
    # 按类别显示参数
    text_params = []
    image_params = []
    
    for key in sorted(MEMBER_DATA.keys()):
        if key.startswith('image_'):
            image_params.append(key)
        else:
            text_params.append(key)
    
    print("\n📝 文本参数:")
    for i, param in enumerate(text_params, 1):
        print(f"  {i:2d}. {param}")
    
    print(f"\n🖼️  图片参数:")
    for i, param in enumerate(image_params, 1):
        print(f"  {i:2d}. {param}")
    
    print(f"\n📊 数据摘要和验证:")
    print_data_summary()
    print_validation_report()
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
