import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import time
from core.database import db
from core.models import QueryParams, ResponseModel
from api.query.analysisFunction import get_time_range, ai_analysis_service
import asyncio

logger = logging.getLogger(__name__)

class CouponTradeTab:
    """券交易模块"""
    
    def __init__(self):
        pass
    
    async def get_coupon_trade_data(self, params: QueryParams) -> ResponseModel:
        """获取券交易数据"""
        try:
            logger.info(f"收到券交易数据查询请求 - bid: {params.bid}, sid: {params.sid}, 查询类型: {params.query_type}")
            
            # 获取时间范围
            start_date, end_date = get_time_range(params.query_type, params.start_date, params.end_date)
            logger.info(f"原始传入日期: {params.start_date} ~ {params.end_date}")
            logger.info(f"实际查询日期: {start_date} ~ {end_date}")
            
            # 只获取当前期间数据（不需要同比和环比）
            logger.info("开始查询券交易数据（仅当前期间）")
            start_time = time.time()
            current_data = await self._get_coupon_data(params.bid, params.sid, start_date, end_date)
            query_time = time.time() - start_time
            
            logger.info(f"券交易数据查询完成，总耗时: {query_time:.3f}秒")
            logger.debug(f"查询结果数量: {len(current_data)}")
            
            # 格式化响应数据（简化版，无同比环比）
            formatted_data = self._format_simple_response_data(current_data)
            
            logger.info(f"券交易数据处理成功 - bid: {params.bid}, sid: {params.sid}")
            return ResponseModel(
                code=200,
                message="获取券交易数据成功",
                data=formatted_data
            )
            
        except Exception as e:
            logger.error(f"获取券交易数据失败: {str(e)}", exc_info=True)
            return ResponseModel(
                code=500,
                message=f"获取券交易数据失败: {str(e)}",
                data=[]
            )
    
    async def _get_coupon_data(self, bid: str, sid: Optional[str], start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """获取指定时间段的券交易数据"""
        try:
            logger.info(f"开始查询wedatas券交易数据 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            start_time = time.time()
            
            # 构建SQL查询
            sql = """
            SELECT
                couponname,
                couponid,
                
                -- 券发放量（发放 - 取消）
                SUM(coupon_send) - SUM(cancel_coupon_send) AS coupon_sent,

                -- 券使用量（使用 - 取消）
                SUM(coupon_used) - SUM(cancel_coupon_used) AS coupon_used,

                -- 使用率 = 使用量 / 发放量（防止除以0）
                ROUND(
                    (SUM(coupon_used) - SUM(cancel_coupon_used)) / 
                    NULLIF(SUM(coupon_send) - SUM(cancel_coupon_send), 0),
                    4
                ) AS coupon_use_rate,

                -- 券抵扣金额 = 使用量 × 单张金额 camount
                SUM((coupon_used - cancel_coupon_used) * camount) AS coupon_discount_amount,

                -- 带动储值消费（储值 - 取消）
                SUM(trade_prepay) - SUM(cancel_trade_prepay) AS trade_prepay_amount,

                -- 带动现金消费（现金 - 取消）
                SUM(trade_cash) - SUM(cancel_trade_cash) AS trade_cash_amount,

                -- 总交易金额（总额 - 取消）
                SUM(trade_amount) - SUM(cancel_trade_amount) AS trade_total_amount

            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN %s AND %s
                AND bid = %s
            """
            
            # 添加sid条件
            params = [start_date.replace('-', ''), end_date.replace('-', ''), bid]
            if sid:
                sql += " AND sid = %s"
                params.append(sid)
            
            sql += """
            GROUP BY couponname, couponid
            ORDER BY (SUM(trade_cash) - SUM(cancel_trade_cash)) DESC
            """
            
            # 使用新的wedatas数据库连接
            async with db.get_wedatas_connection() as conn:
                async with conn.cursor() as cursor:
                    # 调试日志：打印实际执行的SQL和参数
                    logger.info(f"执行SQL: {sql}")
                    logger.info(f"参数: {params}")
                    await cursor.execute(sql, params)
                    results = await cursor.fetchall()
                    
                    # 获取列名
                    columns = [desc[0] for desc in cursor.description]
                    
                    query_time = time.time() - start_time
                    logger.info(f"wedatas券交易数据查询完成，耗时: {query_time:.3f}秒，返回{len(results)}条记录")
                    
                    # 转换为字典列表
                    data = []
                    for row in results:
                        row_dict = dict(zip(columns, row))
                        
                        # 处理金额字段（数据库存储的是实际值*100）
                        if row_dict.get('coupon_discount_amount'):
                            row_dict['coupon_discount_amount'] = float(row_dict['coupon_discount_amount']) / 100
                        if row_dict.get('trade_prepay_amount'):
                            row_dict['trade_prepay_amount'] = float(row_dict['trade_prepay_amount']) / 100
                        if row_dict.get('trade_cash_amount'):
                            row_dict['trade_cash_amount'] = float(row_dict['trade_cash_amount']) / 100
                        if row_dict.get('trade_total_amount'):
                            row_dict['trade_total_amount'] = float(row_dict['trade_total_amount']) / 100
                        
                        # 处理使用率（转换为百分比）
                        if row_dict.get('coupon_use_rate'):
                            row_dict['coupon_use_rate'] = float(row_dict['coupon_use_rate']) * 100
                        
                        # 处理空值
                        for key, value in row_dict.items():
                            if value is None:
                                row_dict[key] = 0
                        
                        data.append(row_dict)
                    
                    logger.debug(f"券交易数据处理完成: {len(data)}条记录")
                    return data
                    
        except Exception as e:
            logger.error(f"查询券交易数据失败: {str(e)}", exc_info=True)
            return []
    
    def _format_simple_response_data(self, current_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """格式化响应数据（简化版，无同比环比）"""
        try:
            logger.debug(f"开始格式化响应数据 - 当前数据: {len(current_data)}条")
            formatted_data = []
            
            for current_item in current_data:
                # 构建简化的响应数据
                formatted_item = {
                    'couponName': current_item.get('couponname', ''),
                    'couponId': current_item.get('couponid', ''),
                    'couponSendCount': int(current_item.get('coupon_sent', 0)),
                    'couponUsedCount': int(current_item.get('coupon_used', 0)),
                    'couponUsageRate': round(float(current_item.get('coupon_use_rate', 0)), 1),
                    'couponDiscountAmount': round(float(current_item.get('coupon_discount_amount', 0)), 2),
                    'drivePrepayAmount': round(float(current_item.get('trade_prepay_amount', 0)), 2),
                    'driveCashAmount': round(float(current_item.get('trade_cash_amount', 0)), 2),
                    'driveTotalAmount': round(float(current_item.get('trade_total_amount', 0)), 2),
                }
                
                formatted_data.append(formatted_item)
            
            logger.debug(f"响应数据格式化完成: {len(formatted_data)}条记录")
            return formatted_data
            
        except Exception as e:
            logger.error(f"格式化响应数据失败: {str(e)}", exc_info=True)
            return []
    
    async def get_ai_analysis(self, params: QueryParams) -> ResponseModel:
        """获取券交易AI分析"""
        try:
            logger.info(f"收到券交易AI分析请求 - bid: {params.bid}, sid: {params.sid}")
            
            # 先获取数据
            data_result = await self.get_coupon_trade_data(params)
            if data_result.code != 200:
                logger.error("获取券交易数据失败，无法进行AI分析")
                return ResponseModel(
                    code=500,
                    message="获取数据失败，无法进行AI分析",
                    data=""
                )
            
            # 准备查询参数
            query_params = {
                "query_type": params.query_type,
                "bid": params.bid,
                "sid": params.sid,
                "start_date": params.start_date,
                "end_date": params.end_date
            }
            
            logger.debug(f"开始AI分析，数据量: {len(data_result.data)}")
            
            # 调用AI分析服务
            analysis_result = await ai_analysis_service.analyze_coupon_trade_data(
                data_result.data, query_params
            )
            
            logger.info(f"券交易AI分析完成 - bid: {params.bid}, sid: {params.sid}")
            return ResponseModel(
                code=200,
                message="获取AI分析成功",
                data=analysis_result
            )
            
        except Exception as e:
            logger.error(f"获取券交易AI分析失败: {str(e)}", exc_info=True)
            return ResponseModel(
                code=500,
                message=f"获取AI分析失败: {str(e)}",
                data="AI分析暂时不可用，请稍后再试。"
            )
