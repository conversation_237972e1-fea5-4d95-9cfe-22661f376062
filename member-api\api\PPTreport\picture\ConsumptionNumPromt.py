# -*- coding: utf-8 -*-
"""
会员消费数量分析AI提示词模板
针对会员消费数量数据分析提供专业的AI分析模板
"""

from typing import Dict, Any, List
import random

class ConsumptionNumAnalysisPrompts:
    """会员消费数量数据AI分析提示词类"""

    # 消费数量数据分析模板句式（去年数据）
    CONSUMPTION_NUM_LAST_YEAR_TEMPLATES = [
        "去年全年消费数据显示，月均消费人数{avg_consume_users:.0f}人，总消费笔数月均{avg_total_consume:.0f}笔，消费频次平均{avg_frequency:.2f}次/人，{trend_analysis}。",
        "去年充值数据表现为月均{avg_charge_count:.0f}笔，充值活跃度{charge_activity_analysis}，{charge_trend_analysis}。",
        "去年消费行为呈现{consumption_pattern}特征，{seasonal_analysis}，{behavior_stability_analysis}。",
        "去年数据基础表明{baseline_analysis}，为今年运营{baseline_significance}。",
        "去年消费质量{quality_analysis}，频次控制{frequency_control_analysis}。"
    ]

    # 消费数量数据分析模板句式（今年数据，包含对比）
    CONSUMPTION_NUM_THIS_YEAR_TEMPLATES = [
        "今年截至目前消费人数累计{total_consume_users}人，月均{avg_consume_users:.0f}人，较去年同期{comparison_trend}，{performance_analysis}。",
        "今年消费笔数数据显示月均{avg_total_consume:.0f}笔，消费频次{avg_frequency:.2f}次/人，相比去年{consume_comparison}，{efficiency_improvement}。",
        "今年充值表现{charge_trend}，{monthly_charge_performance}，{charge_quality_analysis}。",
        "对比去年同期，今年{year_over_year_analysis}，{competitive_position}。",
        "今年消费发展{development_assessment}，{future_projection}。"
    ]

    # 数据趋势分析词汇
    TREND_ANALYSIS_TERMS = {
        "positive": ["呈现稳步上升趋势", "表现出良好增长态势", "显示积极发展势头", "展现强劲增长动力"],
        "negative": ["出现下降趋势", "表现相对疲软", "增长动力不足", "面临增长挑战"],
        "stable": ["保持相对稳定", "波动较小", "增长平稳", "发展稳健"],
        "volatile": ["波动较大", "起伏明显", "变化频繁", "不够稳定"]
    }

    # 消费行为分析词汇
    CONSUMPTION_BEHAVIOR_TERMS = [
        "消费频次稳定，用户粘性较强",
        "消费行为集中，复购率良好",
        "消费分布均匀，运营效果显著",
        "消费波动明显，需要优化策略"
    ]

    # 对比分析词汇
    COMPARISON_TERMS = {
        "better": ["表现更优", "有所改善", "明显提升", "显著改进"],
        "worse": ["有所下降", "表现不如", "需要改进", "存在差距"],
        "similar": ["基本持平", "相差不大", "保持稳定", "变化不明显"]
    }

    @staticmethod
    def get_consumption_num_last_year_analysis_prompt(monthly_data: List[Dict[str, Any]]) -> str:
        """
        生成去年消费数量数据分析的按点格式提示词（洞见性+实操性）

        Args:
            monthly_data: 去年月度消费数量数据列表

        Returns:
            str: 按点格式的分析提示词
        """
        if not monthly_data:
            return "• 数据不足：去年消费数量数据缺失，建议完善数据收集机制"

        # 计算统计数据
        consume_users = [item['consume_users'] for item in monthly_data]
        total_consume_counts = [item['total_consume_count'] for item in monthly_data]
        charge_counts = [item['charge_count'] for item in monthly_data]
        consume_frequencies = [item['consume_frequency'] for item in monthly_data]

        avg_consume_users = sum(consume_users) / len(consume_users)
        avg_total_consume = sum(total_consume_counts) / len(total_consume_counts)
        avg_charge_count = sum(charge_counts) / len(charge_counts)
        avg_frequency = sum(consume_frequencies) / len(consume_frequencies)
        total_consume_users = sum(consume_users)

        # 找出峰值和低谷
        max_consume_idx = consume_users.index(max(consume_users))
        min_consume_idx = consume_users.index(min(consume_users))
        peak_month = monthly_data[max_consume_idx]['month']
        valley_month = monthly_data[min_consume_idx]['month']
        peak_value = max(consume_users)
        valley_value = min(consume_users)

        # 计算波动性
        variance = sum((x - avg_consume_users) ** 2 for x in consume_users) / len(consume_users)
        volatility = (variance ** 0.5) / avg_consume_users * 100 if avg_consume_users > 0 else 0

        # 生成按点分析
        analysis_points = []

        # 1. 消费规模洞察 + 实操建议
        if avg_consume_users > 1000:
            analysis_points.append(f"• 消费规模优异：月均消费人数{avg_consume_users:.0f}人，全年累计{total_consume_users}人，建议维持优质服务并扩大用户基础")
        elif avg_consume_users > 600:
            analysis_points.append(f"• 消费规模稳健：月均消费人数{avg_consume_users:.0f}人，有提升空间，建议优化产品体验并加强用户激活")
        else:
            analysis_points.append(f"• 消费规模偏低：月均消费人数{avg_consume_users:.0f}人，需重点关注，建议重新评估产品定位并制定激活计划")

        # 2. 消费频次洞察 + 实操建议
        if avg_frequency > 3.0:
            analysis_points.append(f"• 消费频次优秀：平均{avg_frequency:.2f}次/人，用户粘性强，建议推出会员权益体系提升用户价值")
        elif avg_frequency > 2.0:
            analysis_points.append(f"• 消费频次良好：平均{avg_frequency:.2f}次/人，有优化空间，建议设计复购激励机制提升频次")
        else:
            analysis_points.append(f"• 消费频次偏低：平均{avg_frequency:.2f}次/人，需要改进，建议分析用户行为并优化产品推荐策略")

        # 3. 充值活跃度洞察 + 实操建议
        charge_ratio = (avg_charge_count / avg_total_consume * 100) if avg_total_consume > 0 else 0
        if charge_ratio > 30:
            analysis_points.append(f"• 充值活跃度高：充值占消费比{charge_ratio:.2f}%，用户信任度强，建议推出储值优惠活动增加用户粘性")
        elif charge_ratio > 15:
            analysis_points.append(f"• 充值活跃度中等：充值占消费比{charge_ratio:.2f}%，有提升空间，建议优化充值体验并增加充值激励")
        else:
            analysis_points.append(f"• 充值活跃度低：充值占消费比{charge_ratio:.2f}%，需要关注，建议分析充值障碍并优化充值流程")

        # 4. 季节性波动洞察 + 实操建议
        if volatility > 25:
            analysis_points.append(f"• 波动性较大：消费人数波动{volatility:.2f}%，峰值{peak_month}({peak_value}人)，建议制定淡季促销策略平滑波动")
        else:
            analysis_points.append(f"• 波动性适中：消费人数相对稳定，波动{volatility:.2f}%，建议保持现有运营节奏并适度优化")

        return '\n'.join(analysis_points)

    @staticmethod
    def get_consumption_num_this_year_analysis_prompt(
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> str:
        """
        生成今年消费数量数据分析的按点格式提示词（包含对比分析）

        Args:
            this_year_data: 今年月度消费数量数据列表
            last_year_data: 去年同期数据列表（用于对比）

        Returns:
            str: 按点格式的对比分析提示词
        """
        if not this_year_data:
            return "• 数据不足：今年消费数量数据缺失，建议完善数据收集机制"

        # 计算今年统计数据
        this_consume_users = [item['consume_users'] for item in this_year_data]
        this_total_consume = [item['total_consume_count'] for item in this_year_data]
        this_charge_counts = [item['charge_count'] for item in this_year_data]
        this_frequencies = [item['consume_frequency'] for item in this_year_data]

        this_avg_consume_users = sum(this_consume_users) / len(this_consume_users)
        this_avg_total_consume = sum(this_total_consume) / len(this_total_consume)
        this_avg_charge = sum(this_charge_counts) / len(this_charge_counts)
        this_avg_frequency = sum(this_frequencies) / len(this_frequencies)
        this_total_users = sum(this_consume_users)

        # 计算去年同期对比数据
        growth_consume_users = 0
        growth_total_consume = 0
        growth_charge = 0
        frequency_change = 0

        if last_year_data:
            # 取去年同期数据（相同月份数量）
            last_year_same_period = last_year_data[:len(this_year_data)]

            last_consume_users = [item['consume_users'] for item in last_year_same_period]
            last_total_consume = [item['total_consume_count'] for item in last_year_same_period]
            last_charge_counts = [item['charge_count'] for item in last_year_same_period]
            last_frequencies = [item['consume_frequency'] for item in last_year_same_period]

            last_avg_consume_users = sum(last_consume_users) / len(last_consume_users)
            last_avg_total_consume = sum(last_total_consume) / len(last_total_consume)
            last_avg_charge = sum(last_charge_counts) / len(last_charge_counts)
            last_avg_frequency = sum(last_frequencies) / len(last_frequencies)

            # 计算增长率
            growth_consume_users = ((this_avg_consume_users - last_avg_consume_users) / last_avg_consume_users * 100) if last_avg_consume_users > 0 else 0
            growth_total_consume = ((this_avg_total_consume - last_avg_total_consume) / last_avg_total_consume * 100) if last_avg_total_consume > 0 else 0
            growth_charge = ((this_avg_charge - last_avg_charge) / last_avg_charge * 100) if last_avg_charge > 0 else 0
            frequency_change = this_avg_frequency - last_avg_frequency

        # 生成按点分析
        analysis_points = []

        # 1. 增长对比洞察 + 实操建议
        if growth_consume_users > 15:
            analysis_points.append(f"• 增长表现卓越：消费人数较去年同期增长{growth_consume_users:.2f}%，累计{this_total_users}人，建议加大成功策略投入并扩展到更多渠道")
        elif growth_consume_users > 5:
            analysis_points.append(f"• 增长表现良好：消费人数较去年同期增长{growth_consume_users:.2f}%，建议分析增长驱动因素并复制成功经验")
        elif growth_consume_users > -5:
            analysis_points.append(f"• 增长表现平稳：消费人数较去年同期变化{growth_consume_users:.2f}%，建议优化用户体验提升活跃度")
        else:
            analysis_points.append(f"• 增长表现下滑：消费人数较去年同期下降{abs(growth_consume_users):.2f}%，建议紧急分析原因并制定挽回策略")

        # 2. 消费效率对比洞察 + 实操建议
        if growth_total_consume > growth_consume_users + 10:
            analysis_points.append(f"• 消费效率提升：消费笔数增长{growth_total_consume:.2f}%超过人数增长，人均消费频次提升，建议继续优化产品体验")
        elif growth_total_consume < growth_consume_users - 10:
            analysis_points.append(f"• 消费效率下降：消费笔数增长{growth_total_consume:.2f}%低于人数增长，需关注用户活跃度，建议推出复购激励")
        else:
            analysis_points.append(f"• 消费效率稳定：消费笔数与人数增长基本匹配，建议保持现有策略并适度优化")

        # 3. 消费频次变化洞察 + 实操建议
        if frequency_change > 0.3:
            analysis_points.append(f"• 消费频次提升：较去年同期提升{frequency_change:.2f}次/人，用户粘性增强，建议推出高频用户专属权益")
        elif frequency_change < -0.3:
            analysis_points.append(f"• 消费频次下降：较去年同期下降{abs(frequency_change):.2f}次/人，需要关注，建议分析用户行为变化并优化产品推荐")
        else:
            analysis_points.append(f"• 消费频次稳定：与去年同期基本持平，建议通过个性化推荐和会员权益进一步提升频次")

        # 4. 充值表现对比洞察 + 实操建议
        if growth_charge > 20:
            analysis_points.append(f"• 充值表现优异：充值笔数较去年同期增长{growth_charge:.2f}%，用户信任度提升，建议推出更多储值优惠活动")
        elif growth_charge > 0:
            analysis_points.append(f"• 充值表现改善：充值笔数较去年同期增长{growth_charge:.2f}%，建议继续优化充值体验并增加激励")
        else:
            analysis_points.append(f"• 充值表现下滑：充值笔数较去年同期下降{abs(growth_charge):.2f}%，建议分析充值障碍并优化充值策略")

        return '\n'.join(analysis_points)