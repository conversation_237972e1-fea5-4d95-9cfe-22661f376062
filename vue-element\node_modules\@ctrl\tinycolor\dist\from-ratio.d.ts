import { TinyColor } from './index.js';
export interface RatioInput {
    r: number | string;
    g: number | string;
    b: number | string;
    a?: number | string;
}
/**
 * If input is an object, force 1 into "1.0" to handle ratios properly
 * String input requires "1.0" as input, so 1 will be treated as 1
 */
export declare function fromRatio(ratio: RatioInput, opts?: any): TinyColor;
/** old random function */
export declare function legacyRandom(): TinyColor;
