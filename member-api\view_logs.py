#!/usr/bin/env python3
"""
日志查看工具
用于查看品智收银模块的详细日志
"""

import os
import sys
from datetime import datetime
import argparse

def view_logs(lines=50, follow=False, filter_text=None):
    """查看日志文件"""
    
    # 获取今天的日志文件
    today = datetime.now().strftime('%Y-%m-%d')
    log_file = f"logs/member_api_{today}.log"
    
    if not os.path.exists(log_file):
        print(f"日志文件不存在: {log_file}")
        print("可能的原因:")
        print("1. 服务还未启动")
        print("2. 日志目录不存在")
        print("3. 今天还没有生成日志")
        return
    
    print(f"查看日志文件: {log_file}")
    print("=" * 60)
    
    if follow:
        # 实时跟踪日志
        print("实时跟踪模式 (按Ctrl+C退出)")
        print("-" * 60)
        try:
            import subprocess
            cmd = ["tail", "-f", log_file]
            if filter_text:
                cmd = ["tail", "-f", log_file, "|", "grep", filter_text]
            subprocess.run(cmd)
        except KeyboardInterrupt:
            print("\n退出实时跟踪")
        except FileNotFoundError:
            # Windows系统没有tail命令，使用Python实现
            _follow_log_python(log_file, filter_text)
    else:
        # 查看最后N行
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                
            # 过滤内容
            if filter_text:
                all_lines = [line for line in all_lines if filter_text in line]
            
            # 显示最后N行
            display_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
            
            for line in display_lines:
                print(line.rstrip())
                
            print("-" * 60)
            print(f"显示了最后 {len(display_lines)} 行日志")
            if filter_text:
                print(f"过滤条件: {filter_text}")
                
        except Exception as e:
            print(f"读取日志文件失败: {e}")

def _follow_log_python(log_file, filter_text=None):
    """Python实现的日志跟踪（适用于Windows）"""
    import time
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            # 移动到文件末尾
            f.seek(0, 2)
            
            while True:
                line = f.readline()
                if line:
                    if not filter_text or filter_text in line:
                        print(line.rstrip())
                else:
                    time.sleep(0.1)
    except KeyboardInterrupt:
        print("\n退出实时跟踪")

def main():
    parser = argparse.ArgumentParser(description='查看品智收银模块日志')
    parser.add_argument('-n', '--lines', type=int, default=50, 
                       help='显示最后N行日志 (默认: 50)')
    parser.add_argument('-f', '--follow', action='store_true',
                       help='实时跟踪日志')
    parser.add_argument('--filter', type=str, 
                       help='过滤包含指定文本的日志行')
    parser.add_argument('--pinzhi', action='store_true',
                       help='只显示品智收银相关日志')
    
    args = parser.parse_args()
    
    # 如果指定了--pinzhi，自动设置过滤条件
    filter_text = args.filter
    if args.pinzhi:
        filter_text = 'PinzhiTab'
    
    view_logs(lines=args.lines, follow=args.follow, filter_text=filter_text)

if __name__ == "__main__":
    main()
