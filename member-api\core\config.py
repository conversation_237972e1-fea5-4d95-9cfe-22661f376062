try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings
from typing import Optional
import os
from pathlib import Path

# 手动加载.env文件确保环境变量正确设置
def load_env_file():
    """手动加载.env文件"""
    env_file = Path(__file__).parent.parent / ".env"
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    # 只有当环境变量不存在时才设置
                    if key not in os.environ:
                        os.environ[key] = value

# 在导入时加载环境变量
load_env_file()

class Settings(BaseSettings):
    # API服务配置
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    API_RELOAD: bool = True
    
    # 灵积配置，通义api配置
    DASHSCOPE_API_KEY: str = "your_api_key_here"
    DASHSCOPE_BASE_URL: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    DASHSCOPE_MODEL: str = "qwen-max"
    
    # MySQL数据库配置 - dwoutput数据库
    DWOUTPUT_DB_HOST: str = "localhost"
    DWOUTPUT_DB_PORT: int = 3306
    DWOUTPUT_DB_USER: str = "your_actual_username"
    DWOUTPUT_DB_PASSWORD: str = "your_actual_password"
    DWOUTPUT_DB_NAME: str = "dwoutput"
    DWOUTPUT_DB_CHARSET: str = "utf8mb4"
    
    # MySQL数据库配置 - wedatas数据库
    WEDATAS_DB_HOST: str = "localhost"
    WEDATAS_DB_PORT: int = 3306
    WEDATAS_DB_USER: str = "your_actual_username"
    WEDATAS_DB_PASSWORD: str = "your_actual_password"
    WEDATAS_DB_NAME: str = "wedatas"
    WEDATAS_DB_CHARSET: str = "utf8mb4"

    # MySQL数据库配置 - welife_hydb数据库
    WELIFE_HYDB_DB_HOST: str = "localhost"
    WELIFE_HYDB_DB_PORT: int = 3306
    WELIFE_HYDB_DB_USER: str = "your_actual_username"
    WELIFE_HYDB_DB_PASSWORD: str = "your_actual_password"
    WELIFE_HYDB_DB_NAME: str = "welife_hydb"
    WELIFE_HYDB_DB_CHARSET: str = "utf8mb4"

    # PostgreSQL数据库配置 - 品质收银数据库
    # 注意：这些配置需要根据实际的品智收银数据库信息进行修改
    # 从您的SQL查询可以看出，真实数据库包含xianglala.rept_ognperformance表
    POS_DW_DB_HOST: str = "your_actual_pinzhi_host"  # 请替换为真实的品智收银数据库主机
    POS_DW_DB_PORT: int = 5432
    POS_DW_DB_USER: str = "your_actual_pinzhi_username"  # 请替换为真实的用户名
    POS_DW_DB_PASSWORD: str = "your_actual_pinzhi_password"  # 请替换为真实的密码
    POS_DW_DB_NAME: str = "your_actual_pinzhi_database"  # 请替换为真实的数据库名
    POS_DW_DB_CHARSET: str = "utf8"
    
    # 数据库连接池配置
    DB_POOL_MIN_SIZE: int = 5
    DB_POOL_MAX_SIZE: int = 20
    
    # 数据库连接超时配置
    DB_CONNECT_TIMEOUT: int = 30
    DB_READ_TIMEOUT: int = 30
    DB_WRITE_TIMEOUT: int = 30
    
    # 数据库重连配置
    DB_AUTO_RECONNECT: bool = True
    DB_MAX_RETRIES: int = 3
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE: str = "logs/app.log"
    
    # 环境配置
    ENVIRONMENT: str = "development"  # development, production, testing
    
    # OSS配置（用于存储分析结果）
    OSS_ACCESS_KEY_ID: str = "your_oss_access_key_id"
    OSS_ACCESS_KEY_SECRET: str = "your_oss_access_key_secret"
    OSS_BUCKET_NAME: str = "your_oss_bucket_name"
    OSS_ENDPOINT: str = "https://oss-cn-hangzhou.aliyuncs.com"
    OSS_REGION: str = "oss-cn-hangzhou"

    # 本地文件服务配置（用于PPT文件存储）
    FILE_UPLOAD_PATH: str = "./uploads"
    FILE_BASE_URL: str = "http://localhost:8000"
    FILE_MAX_SIZE_MB: int = 50
    FILE_ALLOWED_EXTENSIONS: str = ".pptx,.pdf,.docx,.xlsx"
    FILE_URL_EXPIRE_HOURS: int = 24
    
    # 兼容旧的单数据库配置（向后兼容，已弃用）
    CLIENT_DB_HOST: str = "localhost"
    CLIENT_DB_PORT: int = 3306
    CLIENT_DB_USER: str = "your_actual_username"
    CLIENT_DB_PASSWORD: str = "your_actual_password"
    CLIENT_DB_NAME: str = "dwoutput"  # 默认使用dwoutput数据库
    CLIENT_DB_CHARSET: str = "utf8mb4"
    
    # 数据库健康检查配置
    DB_HEALTH_CHECK_INTERVAL: int = 60  # 秒
    DB_HEALTH_CHECK_ENABLED: bool = True
    
    # 查询性能监控配置
    DB_QUERY_SLOW_THRESHOLD: float = 1.0  # 秒，超过此时间的查询会被记录为慢查询
    DB_QUERY_LOG_ENABLED: bool = True
    
    # 并发控制配置
    DB_MAX_CONCURRENT_QUERIES: int = 50
    
    # 数据库特定配置
    DWOUTPUT_DB_DESCRIPTION: str = "用于存储DWOutput相关数据的数据库"
    WEDATAS_DB_DESCRIPTION: str = "用于存储WeData相关数据的数据库"
    WELIFE_HYDB_DB_DESCRIPTION: str = "用于存储Welife相关数据的数据库"
    POS_DW_DB_DESCRIPTION: str = "品质收银数据库，用于存储POS相关数据"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        # 允许额外的字段
        extra = "ignore"
        # 确保环境变量优先级高于默认值
        env_prefix = ""
        
    def get_dwoutput_db_url(self) -> str:
        """获取dwoutput数据库连接URL"""
        return f"mysql+aiomysql://{self.DWOUTPUT_DB_USER}:{self.DWOUTPUT_DB_PASSWORD}@{self.DWOUTPUT_DB_HOST}:{self.DWOUTPUT_DB_PORT}/{self.DWOUTPUT_DB_NAME}?charset={self.DWOUTPUT_DB_CHARSET}"
    
    def get_wedatas_db_url(self) -> str:
        """获取wedatas数据库连接URL"""
        return f"mysql+aiomysql://{self.WEDATAS_DB_USER}:{self.WEDATAS_DB_PASSWORD}@{self.WEDATAS_DB_HOST}:{self.WEDATAS_DB_PORT}/{self.WEDATAS_DB_NAME}?charset={self.WEDATAS_DB_CHARSET}"

    def get_welife_hydb_db_url(self) -> str:
        """获取welife_hydb数据库连接URL"""
        return f"mysql+aiomysql://{self.WELIFE_HYDB_DB_USER}:{self.WELIFE_HYDB_DB_PASSWORD}@{self.WELIFE_HYDB_DB_HOST}:{self.WELIFE_HYDB_DB_PORT}/{self.WELIFE_HYDB_DB_NAME}?charset={self.WELIFE_HYDB_DB_CHARSET}"

    def get_pos_dw_db_url(self) -> str:
        """获取品质收银数据库连接URL"""
        return f"postgresql+asyncpg://{self.POS_DW_DB_USER}:{self.POS_DW_DB_PASSWORD}@{self.POS_DW_DB_HOST}:{self.POS_DW_DB_PORT}/{self.POS_DW_DB_NAME}"
    
    def is_production(self) -> bool:
        """判断是否为生产环境"""
        return self.ENVIRONMENT.lower() == "production"
    
    def is_development(self) -> bool:
        """判断是否为开发环境"""
        return self.ENVIRONMENT.lower() == "development"
    
    def get_db_pool_config(self) -> dict:
        """获取数据库连接池配置"""
        return {
            "minsize": self.DB_POOL_MIN_SIZE,
            "maxsize": self.DB_POOL_MAX_SIZE,
            "connect_timeout": self.DB_CONNECT_TIMEOUT,
            "autocommit": True,
            "echo": self.is_development(),  # 开发环境下打印SQL
        }

    def get_file_service_config(self) -> dict:
        """获取文件服务配置"""
        return {
            "base_url": self.FILE_BASE_URL,
            "upload_path": self.FILE_UPLOAD_PATH,
            "max_file_size": self.FILE_MAX_SIZE_MB * 1024 * 1024,  # 转换为字节
            "allowed_extensions": [ext.strip() for ext in self.FILE_ALLOWED_EXTENSIONS.split(",")],
            "url_expire_hours": self.FILE_URL_EXPIRE_HOURS
        }

settings = Settings()

# 配置调试信息
import logging
config_logger = logging.getLogger("config")
config_logger.info("=== 配置加载调试信息 ===")
config_logger.info(f"DASHSCOPE_API_KEY: {'已配置' if settings.DASHSCOPE_API_KEY != 'your_api_key_here' else '未配置(使用默认值)'}")
config_logger.info(f"DASHSCOPE_BASE_URL: {settings.DASHSCOPE_BASE_URL}")
config_logger.info(f"DASHSCOPE_MODEL: {settings.DASHSCOPE_MODEL}")
config_logger.info(f"FILE_BASE_URL: {settings.FILE_BASE_URL}")
config_logger.info(f"FILE_UPLOAD_PATH: {settings.FILE_UPLOAD_PATH}")

# 检查环境变量
import os
config_logger.info("=== 环境变量检查 ===")
config_logger.info(f"环境变量 DASHSCOPE_API_KEY: {'已设置' if os.getenv('DASHSCOPE_API_KEY') else '未设置'}")
config_logger.info(f"环境变量 DASHSCOPE_BASE_URL: {os.getenv('DASHSCOPE_BASE_URL', '未设置')}")
config_logger.info(f"环境变量 FILE_BASE_URL: {os.getenv('FILE_BASE_URL', '未设置')}")

# 配置验证
def validate_config():
    """验证配置的有效性"""
    import logging
    logger = logging.getLogger(__name__)
    
    # 检查必要的数据库配置
    required_db_configs = [
        ('DWOUTPUT_DB_HOST', settings.DWOUTPUT_DB_HOST),
        ('DWOUTPUT_DB_USER', settings.DWOUTPUT_DB_USER),
        ('DWOUTPUT_DB_PASSWORD', settings.DWOUTPUT_DB_PASSWORD),
        ('DWOUTPUT_DB_NAME', settings.DWOUTPUT_DB_NAME),
        ('WEDATAS_DB_HOST', settings.WEDATAS_DB_HOST),
        ('WEDATAS_DB_USER', settings.WEDATAS_DB_USER),
        ('WEDATAS_DB_PASSWORD', settings.WEDATAS_DB_PASSWORD),
        ('WEDATAS_DB_NAME', settings.WEDATAS_DB_NAME),
        ('WELIFE_HYDB_DB_HOST', settings.WELIFE_HYDB_DB_HOST),
        ('WELIFE_HYDB_DB_USER', settings.WELIFE_HYDB_DB_USER),
        ('WELIFE_HYDB_DB_PASSWORD', settings.WELIFE_HYDB_DB_PASSWORD),
        ('WELIFE_HYDB_DB_NAME', settings.WELIFE_HYDB_DB_NAME),
    ]
    
    for config_name, config_value in required_db_configs:
        if not config_value or config_value.startswith('your_'):
            logger.warning(f"配置项 {config_name} 使用默认值或未设置，请检查 .env 文件")
    
    # 检查端口配置
    if not (1 <= settings.API_PORT <= 65535):
        logger.error(f"API_PORT 配置无效: {settings.API_PORT}")
        raise ValueError(f"API_PORT 必须在 1-65535 范围内")
    
    if not (1 <= settings.DWOUTPUT_DB_PORT <= 65535):
        logger.error(f"DWOUTPUT_DB_PORT 配置无效: {settings.DWOUTPUT_DB_PORT}")
        raise ValueError(f"DWOUTPUT_DB_PORT 必须在 1-65535 范围内")
    
    if not (1 <= settings.WEDATAS_DB_PORT <= 65535):
        logger.error(f"WEDATAS_DB_PORT 配置无效: {settings.WEDATAS_DB_PORT}")
        raise ValueError(f"WEDATAS_DB_PORT 必须在 1-65535 范围内")

    if not (1 <= settings.WELIFE_HYDB_DB_PORT <= 65535):
        logger.error(f"WELIFE_HYDB_DB_PORT 配置无效: {settings.WELIFE_HYDB_DB_PORT}")
        raise ValueError(f"WELIFE_HYDB_DB_PORT 必须在 1-65535 范围内")
    
    # 检查连接池配置
    if settings.DB_POOL_MIN_SIZE > settings.DB_POOL_MAX_SIZE:
        logger.error("数据库连接池最小连接数不能大于最大连接数")
        raise ValueError("DB_POOL_MIN_SIZE 不能大于 DB_POOL_MAX_SIZE")
    
    logger.info("配置验证完成")

# 在导入时进行配置验证
try:
    validate_config()
except Exception as e:
    import logging
    logging.getLogger(__name__).error(f"配置验证失败: {e}")
    # 在生产环境中，配置错误应该导致应用启动失败
    if settings.is_production():
        raise