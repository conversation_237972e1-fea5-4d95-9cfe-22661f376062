# -*- coding: utf-8 -*-
"""
PPT报告路由配置
定义PPTreport模块的API端点
"""

import logging
import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel, Field

from core.models import QueryParams, ResponseModel
from .PPTReport import ppt_report_service

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

def _format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"

    size_mb = size_bytes / (1024 * 1024)
    if size_mb >= 1:
        return f"{size_mb:.1f}MB"
    else:
        size_kb = size_bytes / 1024
        return f"{size_kb:.1f}KB"

class PPTGenerationRequest(BaseModel):
    """PPT生成请求模型"""
    query_type: str = Field(..., description="查询类型")
    bid: str = Field(..., description="品牌ID")
    sid: Optional[str] = Field(None, description="门店ID")
    start_date: Optional[str] = Field(None, description="开始日期")
    end_date: Optional[str] = Field(None, description="结束日期")
    cashier_system: Optional[str] = Field("0", description="收银系统类型：0-无收银系统，1-品智收银")
    merchant_id: Optional[str] = Field(None, description="商户ID（品智收银门店拼音名称）")
    output_filename: Optional[str] = Field(None, description="输出文件名（可选）")

class CustomDataRequest(BaseModel):
    """自定义数据请求模型"""
    custom_data: Dict[str, Any] = Field(..., description="自定义PPT参数数据")
    output_filename: Optional[str] = Field(None, description="输出文件名（可选）")

@router.post("/generate", response_model=ResponseModel)
async def generate_member_report(request: PPTGenerationRequest):
    """
    生成会员数据报告PPT

    Args:
        request: PPT生成请求

    Returns:
        ResponseModel: 统一响应格式
    """
    try:
        logger.info(f"收到PPT生成请求: {request}")

        # 转换为QueryParams格式
        query_params = QueryParams(
            query_type=request.query_type,
            bid=request.bid,
            sid=request.sid,
            start_date=request.start_date,
            end_date=request.end_date,
            cashier_system=request.cashier_system,
            merchant_id=request.merchant_id
        )

        result = await ppt_report_service.generate_member_report(
            query_params=query_params,
            output_filename=request.output_filename
        )

        if result["success"]:
            # 转换为前端期望的格式
            download_url = result["data"].get("file_url") or result["data"].get("download_url")
            file_name = result["data"].get("file_name")
            object_name = result["data"].get("object_name")

            # 详细的调试日志
            logger.info(f"PPT生成结果详情:")
            logger.info(f"  - 文件名: {file_name}")
            logger.info(f"  - 对象名: {object_name}")
            logger.info(f"  - 原始下载URL: {download_url}")
            logger.info(f"  - 所有返回数据: {result['data']}")

            response_data = {
                "downloadUrl": download_url,
                "previewUrl": None,  # 暂时不支持预览
                "generatedAt": result["data"].get("upload_time") or datetime.datetime.now().isoformat(),
                "slideCount": 12,  # 默认值，后续可以从PPT文件中获取
                "fileSize": _format_file_size(result["data"].get("file_size", 0)),
                "fileName": file_name,
                "objectName": object_name
            }

            # 验证下载URL是否有效
            if not download_url:
                logger.warning("PPT生成成功但下载URL为空，尝试生成默认URL")
                if object_name:
                    response_data["downloadUrl"] = f"/api/ppt-report/download/ppt-reports/{object_name}"
                    logger.info(f"使用对象名生成默认下载URL: {response_data['downloadUrl']}")
                elif file_name:
                    response_data["downloadUrl"] = f"/api/ppt-report/download/ppt-reports/{file_name}"
                    logger.info(f"使用文件名生成默认下载URL: {response_data['downloadUrl']}")
                else:
                    logger.error("无法生成下载URL：文件名和对象名都为空")
            else:
                logger.info(f"使用OSS生成的下载URL: {download_url}")

            return ResponseModel(
                code=200,
                message=result["message"],
                data=response_data
            )
        else:
            return ResponseModel(
                code=500,
                message=result["message"],
                data=None
            )

    except Exception as e:
        logger.error(f"生成PPT报告异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"生成PPT报告失败: {str(e)}")

@router.post("/preview", response_model=ResponseModel)
async def get_data_preview(request: PPTGenerationRequest):
    """
    获取数据预览（不生成PPT，仅返回数据）

    Args:
        request: PPT生成请求

    Returns:
        ResponseModel: 数据预览结果
    """
    try:
        logger.info(f"收到数据预览请求: {request}")

        # 转换为QueryParams格式
        query_params = QueryParams(
            query_type=request.query_type,
            bid=request.bid,
            sid=request.sid,
            start_date=request.start_date,
            end_date=request.end_date
        )

        result = await ppt_report_service.get_data_preview(query_params)

        if result["success"]:
            return ResponseModel(
                code=200,
                message=result["message"],
                data=result["data"]
            )
        else:
            return ResponseModel(
                code=500,
                message=result["message"],
                data=result["data"]
            )

    except Exception as e:
        logger.error(f"获取数据预览异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取数据预览失败: {str(e)}")

@router.post("/generate-custom", response_model=ResponseModel)
async def generate_with_custom_data(request: CustomDataRequest):
    """
    使用自定义数据生成PPT

    Args:
        request: 自定义数据请求

    Returns:
        ResponseModel: 生成结果
    """
    try:
        logger.info(f"收到自定义数据PPT生成请求")

        result = await ppt_report_service.generate_with_custom_data(
            custom_data=request.custom_data,
            output_filename=request.output_filename
        )

        if result["success"]:
            return ResponseModel(
                code=200,
                message=result["message"],
                data=result["data"]
            )
        else:
            return ResponseModel(
                code=500,
                message=result["message"],
                data=result["data"]
            )

    except Exception as e:
        logger.error(f"使用自定义数据生成PPT异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"使用自定义数据生成PPT失败: {str(e)}")

@router.get("/template/validate", response_model=ResponseModel)
async def validate_template():
    """
    验证PPT模板

    Returns:
        ResponseModel: 验证结果
    """
    try:
        logger.info("收到PPT模板验证请求")

        result = await ppt_report_service.validate_ppt_template()

        if result["valid"]:
            return ResponseModel(
                code=200,
                message=result["message"],
                data=result
            )
        else:
            return ResponseModel(
                code=400,
                message=result["message"],
                data=result
            )

    except Exception as e:
        logger.error(f"验证PPT模板异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"验证PPT模板失败: {str(e)}")

@router.get("/template/info", response_model=ResponseModel)
async def get_template_info():
    """
    获取PPT模板信息

    Returns:
        ResponseModel: 模板信息
    """
    try:
        logger.info("收到获取PPT模板信息请求")

        from services.ppt_service import ppt_service
        template_info = ppt_service.get_template_info()

        return ResponseModel(
            code=200,
            message="获取PPT模板信息成功",
            data=template_info
        )

    except Exception as e:
        logger.error(f"获取PPT模板信息异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取PPT模板信息失败: {str(e)}")

@router.api_route("/download/{folder}/{filename}", methods=["GET", "HEAD"])
async def download_file(folder: str, filename: str, request: Request = None):
    """
    下载PPT文件

    Args:
        folder: 文件夹名称
        filename: 文件名

    Returns:
        文件下载响应
    """
    try:
        from fastapi.responses import FileResponse
        from services.oss_service import oss_service
        import os

        import urllib.parse

        # URL解码文件名（处理中文文件名）
        decoded_filename = urllib.parse.unquote(filename)

        logger.info(f"收到文件下载请求: {folder}/{filename}")
        logger.info(f"解码后的文件名: {decoded_filename}")

        # 安全检查：防止路径遍历攻击
        if ".." in folder or ".." in filename or ".." in decoded_filename:
            logger.warning(f"检测到潜在的路径遍历攻击: {folder}/{filename}")
            raise HTTPException(status_code=400, detail="无效的文件路径")

        # 获取文件信息（使用解码后的文件名）
        download_info = oss_service.download_file(decoded_filename, folder)

        if not download_info["success"]:
            logger.warning(f"文件下载失败: {download_info['error']}")
            raise HTTPException(status_code=404, detail=download_info["error"])

        file_path = download_info["file_path"]

        # 验证文件是否真实存在
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            raise HTTPException(status_code=404, detail=f"文件不存在: {filename}")

        # 验证文件大小
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            logger.warning(f"文件大小为0: {file_path}")
            raise HTTPException(status_code=400, detail="文件为空")

        logger.info(f"开始处理文件请求: {file_path} (大小: {file_size} bytes)")

        # 处理HEAD请求（用于前端预检查）
        if request and request.method == "HEAD":
            from fastapi.responses import Response

            logger.info(f"处理HEAD请求: {folder}/{decoded_filename}")

            # 正确处理中文文件名编码
            # 使用RFC 5987标准的编码方式
            encoded_filename = urllib.parse.quote(decoded_filename, safe='')
            content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"

            return Response(
                status_code=200,
                headers={
                    "Content-Type": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                    "Content-Length": str(file_size),
                    "Content-Disposition": content_disposition
                }
            )

        # 处理GET请求（实际下载）
        logger.info(f"开始下载文件: {file_path}")
        return FileResponse(
            path=file_path,
            filename=decoded_filename,  # 使用解码后的文件名
            media_type='application/vnd.openxmlformats-officedocument.presentationml.presentation'
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载文件异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"下载文件失败: {str(e)}")

@router.get("/test-download")
async def test_download():
    """
    测试下载功能

    Returns:
        测试文件下载信息
    """
    try:
        from services.oss_service import oss_service

        # 列出可用的文件
        files_info = oss_service.list_files("ppt-reports")

        if files_info["success"] and files_info["files"]:
            return ResponseModel(
                code=200,
                message="获取文件列表成功",
                data={
                    "available_files": files_info["files"],
                    "total_count": files_info["total_count"],
                    "download_base_url": "/api/ppt-report/download/ppt-reports/"
                }
            )
        else:
            return ResponseModel(
                code=404,
                message="没有找到可下载的文件",
                data={
                    "available_files": [],
                    "total_count": 0,
                    "upload_path": str(oss_service.upload_path / "ppt-reports")
                }
            )

    except Exception as e:
        logger.error(f"测试下载功能异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"测试下载功能失败: {str(e)}")