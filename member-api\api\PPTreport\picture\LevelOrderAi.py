# -*- coding: utf-8 -*-
"""
会员等级订单分析AI分析器
提供会员等级订单数据的智能分析功能
"""

import logging
from typing import Dict, Any, List

from services.llm_service import LLMService

logger = logging.getLogger(__name__)

class LevelOrderAiAnalyzer:
    """会员等级订单分析AI分析器"""

    def __init__(self):
        """初始化会员等级订单分析AI分析器"""
        self.llm_service = LLMService()
        logger.info("会员等级订单分析AI分析器初始化完成")

    async def analyze_level_order_data(self, level_data: List[Dict[str, Any]], start_date: str = None, end_date: str = None) -> str:
        """
        分析会员等级订单数据

        Args:
            level_data: 会员等级订单数据列表
            start_date: 查询开始日期 (YYYY-MM-DD格式)
            end_date: 查询结束日期 (YYYY-MM-DD格式)

        Returns:
            str: AI分析结果（100-150字的整段分析）
        """
        try:
            if not level_data:
                return "会员等级订单数据不足，无法进行有效分析。建议完善数据收集机制，确保各等级会员订单数据的完整性和准确性，为精准营销和会员价值提升提供可靠的数据支撑。"

            # 构建时间范围描述
            time_range_desc = ""
            if start_date and end_date:
                time_range_desc = f"分析时间范围：{start_date} 至 {end_date}"
            elif start_date:
                time_range_desc = f"分析时间范围：{start_date} 开始"
            elif end_date:
                time_range_desc = f"分析时间范围：截至 {end_date}"
            else:
                time_range_desc = "分析时间范围：未指定时间范围"

            # 构建完整的AI分析请求
            full_prompt = f"""
作为专业的会员运营数据分析师，请基于以下会员等级订单数据进行深入分析：

{time_range_desc}

数据概况：
{self._format_level_order_data_summary(level_data, start_date, end_date)}

分析要求：
请生成一段200-250字的专业分析报告，要求：
1. 整段输出，不要分点或分段
2. 重点关注：等级间订单差异、消费金额占比、订单数量占比、复购率表现
3. 避免简单的数据描述，要提供深度洞察和实操建议
4. 确保建议具体可执行，避免空泛的建议
5. 语言简洁专业，逻辑清晰连贯
6. 字数控制在200-250字之间

请生成专业的会员等级订单分析报告：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                # 确保结果长度在合理范围内
                result = analysis_result.strip()
                if len(result) > 350:
                    # 如果太长，截取前150字并确保句子完整
                    result = result[:300]
                    last_period = result.rfind('。')
                    if last_period > 250:
                        result = result[:last_period + 1]

                logger.info(f"会员等级订单数据AI分析完成，分析长度: {len(result)}字")
                return result
            else:
                logger.warning("AI分析返回空结果，使用默认分析")
                return self._get_fallback_analysis(level_data)

        except Exception as e:
            logger.error(f"会员等级订单数据AI分析失败: {str(e)}")
            # 返回基础分析作为备用
            return self._get_fallback_analysis(level_data)

    def _format_level_order_data_summary(self, level_data: List[Dict[str, Any]], start_date: str = None, end_date: str = None) -> str:
        """
        格式化会员等级订单数据摘要

        Args:
            level_data: 会员等级订单数据列表
            start_date: 查询开始日期
            end_date: 查询结束日期

        Returns:
            str: 格式化的数据摘要
        """
        if not level_data:
            return "无数据"

        try:
            # 提取数据
            level_names = [item.get('ccName', '未知等级') for item in level_data]
            money_ratio_values = [item.get('orderMoneyRatio', 0) for item in level_data]
            num_ratio_values = [item.get('orderNumRatio', 0) for item in level_data]
            repurchase_values = [item.get('repurchaseRate', 0) for item in level_data]

            # 计算统计指标
            total_levels = len(level_data)
            avg_money_ratio = sum(money_ratio_values) / total_levels if total_levels > 0 else 0
            avg_num_ratio = sum(num_ratio_values) / total_levels if total_levels > 0 else 0
            avg_repurchase = sum(repurchase_values) / total_levels if total_levels > 0 else 0

            # 找出极值
            max_money_ratio = max(money_ratio_values) if money_ratio_values else 0
            min_money_ratio = min(money_ratio_values) if money_ratio_values else 0
            max_num_ratio = max(num_ratio_values) if num_ratio_values else 0
            min_num_ratio = min(num_ratio_values) if num_ratio_values else 0
            max_repurchase = max(repurchase_values) if repurchase_values else 0
            min_repurchase = min(repurchase_values) if repurchase_values else 0

            # 找出对应的等级名称
            max_money_ratio_level = level_names[money_ratio_values.index(max_money_ratio)] if money_ratio_values else "未知"
            max_num_ratio_level = level_names[num_ratio_values.index(max_num_ratio)] if num_ratio_values else "未知"
            max_repurchase_level = level_names[repurchase_values.index(max_repurchase)] if repurchase_values else "未知"

            # 构建时间范围信息
            time_info = ""
            if start_date and end_date:
                time_info = f"数据时间范围：{start_date} 至 {end_date}\n"
            elif start_date:
                time_info = f"数据时间范围：{start_date} 开始\n"
            elif end_date:
                time_info = f"数据时间范围：截至 {end_date}\n"

            summary = f"""
{time_info}等级数量：{total_levels}个会员等级
等级名称：{', '.join(level_names)}
消费金额占比：平均{avg_money_ratio:.2f}%，最高{max_money_ratio:.2f}%({max_money_ratio_level})，最低{min_money_ratio:.2f}%
消费订单占比：平均{avg_num_ratio:.2f}%，最高{max_num_ratio:.2f}%({max_num_ratio_level})，最低{min_num_ratio:.2f}%
复购率：平均{avg_repurchase:.2f}%，最高{max_repurchase:.2f}%({max_repurchase_level})，最低{min_repurchase:.2f}%
等级差异：消费金额占比差距{max_money_ratio - min_money_ratio:.2f}%，订单占比差距{max_num_ratio - min_num_ratio:.2f}%，复购率差距{max_repurchase - min_repurchase:.2f}%
详细数据：{[f"{level_names[i]}(金额{money_ratio_values[i]:.2f}%/订单{num_ratio_values[i]:.2f}%/复购{repurchase_values[i]:.2f}%)" for i in range(len(level_names))]}
"""
            return summary.strip()

        except Exception as e:
            logger.error(f"格式化会员等级订单数据摘要失败: {str(e)}")
            return f"数据格式化失败: {str(e)}"

    def _get_fallback_analysis(self, level_data: List[Dict[str, Any]]) -> str:
        """
        获取备用分析结果

        Args:
            level_data: 会员等级订单数据列表

        Returns:
            str: 备用分析结果
        """
        if not level_data:
            return "会员等级订单数据不足，无法进行有效分析。建议完善数据收集机制，确保各等级会员订单数据的完整性和准确性。"

        try:
            # 简单的数据分析
            level_count = len(level_data)
            money_ratios = [item.get('orderMoneyRatio', 0) for item in level_data]
            repurchase_rates = [item.get('repurchaseRate', 0) for item in level_data]

            max_money_ratio = max(money_ratios) if money_ratios else 0
            max_repurchase = max(repurchase_rates) if repurchase_rates else 0

            return f"当前共有{level_count}个会员等级，消费金额占比最高达{max_money_ratio:.1f}%，复购率最高为{max_repurchase:.1f}%。建议针对高价值等级会员制定专属营销策略，通过个性化服务和精准推荐提升复购率，同时优化低等级会员的消费体验，促进等级提升和消费增长。"

        except Exception as e:
            logger.error(f"生成备用分析失败: {str(e)}")
            return "会员等级订单数据分析遇到技术问题，建议检查数据完整性后重新分析。"


# 创建全局分析器实例
level_order_ai_analyzer = LevelOrderAiAnalyzer()