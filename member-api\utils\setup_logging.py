import logging
import sys
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler

def setup_logging():
    """配置全局日志格式 - 终端简洁模式，详细日志输出到文件"""

    # 清除现有的handlers
    logging.root.handlers.clear()

    # 创建日志目录
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 设置根日志级别
    logging.root.setLevel(logging.INFO)

    # 禁用第三方库的调试日志
    logging.getLogger('multipart').setLevel(logging.WARNING)
    logging.getLogger('openai').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('oss2').setLevel(logging.WARNING)
    logging.getLogger('uvicorn.access').setLevel(logging.WARNING)

    # 配置详细日志格式（用于文件）
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        '%Y-%m-%d %H:%M:%S'
    )

    # 配置简洁日志格式（用于终端）
    simple_formatter = logging.Formatter(
        '%(levelname)s - %(message)s'
    )

    # 配置文件处理器 - 详细日志
    today = datetime.now().strftime('%Y-%m-%d')
    log_file = os.path.join(log_dir, f'member_api_{today}.log')
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setFormatter(detailed_formatter)
    file_handler.setLevel(logging.INFO)

    # 配置控制台处理器 - 简洁模式，只显示重要信息
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(simple_formatter)
    console_handler.setLevel(logging.WARNING)  # 只显示WARNING及以上级别

    # 为根logger添加handlers
    logging.root.addHandler(file_handler)
    logging.root.addHandler(console_handler)

    # 配置特定模块的日志级别
    # 品智收银模块 - 详细日志输出到文件
    pinzhi_logger = logging.getLogger('api.query.PinzhiOrder.PinzhiTab')
    pinzhi_logger.setLevel(logging.INFO)

    # 应用启动信息仍然显示在终端
    startup_logger = logging.getLogger('startup')
    startup_console = logging.StreamHandler(sys.stdout)
    startup_console.setFormatter(logging.Formatter('%(message)s'))
    startup_logger.addHandler(startup_console)
    startup_logger.setLevel(logging.INFO)
    startup_logger.propagate = False  # 不传播到根logger

    # 输出配置信息
    startup_logger.info("日志配置完成:")
    startup_logger.info(f"  - 详细日志文件: {log_file}")
    startup_logger.info(f"  - 终端模式: 简洁模式（仅显示WARNING及以上）")
    startup_logger.info(f"  - 文件日志: 包含所有INFO级别日志")