from fastapi import APIRouter
from .MemberBaseTab import router as member_base_router
from .MemberConsumeTab import router as member_consume_router
from .MemberChargeTab import router as member_charge_router
from .MemberDataQuery import router as member_data_router

# 创建查询模块的主路由
router = APIRouter()

# 注册统一数据查询路由
router.include_router(member_data_router, prefix="/data", tags=["会员数据查询"])

# 注册子路由
router.include_router(member_base_router, prefix="/member-base", tags=["会员基础数据"])
router.include_router(member_consume_router, prefix="/member-consume", tags=["会员消费数据"])
router.include_router(member_charge_router, prefix="/member-charge", tags=["会员充值数据"])

# 券交易模块和品智收银模块路由在MemberDataQuery中统一处理
