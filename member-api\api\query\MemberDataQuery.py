from fastapi import APIRouter, HTTPException, Depends
from typing import Optional, Dict, Any
import logging
import asyncio

from core.models import QueryParams, MemberDataResponse, ResponseModel
from .MemberBaseTab import member_base_service
from .MemberConsumeTab import member_consume_service
from .MemberChargeTab import member_charge_service
from .CouponTradeTab import CouponTradeTab
from .PinzhiOrder.PinzhiTab import pinzhi_cashier_service
# 🔥 新增：导入品智收银品牌验证函数
from .PinzhiOrder.PinZhiConstant import validate_bid_for_pinzhi, get_validation_error_message
from .analysisFunction import ai_analysis_service

logger = logging.getLogger(__name__)

router = APIRouter()

class MemberDataService:
    """会员数据查询统一服务"""
    
    def __init__(self):
        self.member_base_service = member_base_service
        self.member_consume_service = member_consume_service
        self.member_charge_service = member_charge_service
        self.coupon_trade_service = None
        self.pinzhi_cashier_service = pinzhi_cashier_service
    
    async def get_all_member_data(self, query_params: QueryParams) -> MemberDataResponse:
        """获取所有会员数据"""
        try:
            # 并行获取各模块数据
            tasks = []

            # 会员基础数据
            tasks.append(self.member_base_service.get_member_base_data(query_params))
            # 会员消费数据
            tasks.append(self.member_consume_service.get_member_consume_data(query_params))
            # 会员充值数据
            tasks.append(self.member_charge_service.get_member_charge_data(query_params))

            # 品智收银数据（仅当收银系统为品智收银时获取）
            pinzhi_task = None
            if getattr(query_params, 'cashier_system', '0') == '1':
                pinzhi_task = self.pinzhi_cashier_service.get_pinzhi_cashier_data(query_params)
                tasks.append(pinzhi_task)

            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            member_base_data = results[0] if not isinstance(results[0], Exception) else None
            member_consume_data = results[1] if not isinstance(results[1], Exception) else None
            member_charge_data = results[2] if not isinstance(results[2], Exception) else None

            # 品智收银数据处理
            pinzhi_cashier_data = None
            if pinzhi_task and len(results) > 3:
                pinzhi_cashier_data = results[3] if not isinstance(results[3], Exception) else None

            # 记录错误
            module_names = ["会员基础数据", "会员消费数据", "会员充值数据"]
            if pinzhi_task:
                module_names.append("品智收银数据")

            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"获取{module_names[i]}失败: {str(result)}")

            # 构建响应
            response = MemberDataResponse()

            if member_base_data:
                response.member_base = member_base_data
            if member_consume_data:
                response.member_consume = member_consume_data
            if member_charge_data:
                response.member_charge = member_charge_data
            if pinzhi_cashier_data:
                response.pinzhi_cashier = pinzhi_cashier_data

            # 其他模块数据暂时使用默认值
            # TODO: 添加券交易模块的数据获取

            return response
            
        except Exception as e:
            logger.error(f"获取会员数据失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取会员数据失败: {str(e)}")
    
    async def get_coupon_trade_service(self):
        """获取券交易服务实例"""
        if self.coupon_trade_service is None:
            self.coupon_trade_service = CouponTradeTab()
        return self.coupon_trade_service


# 创建服务实例
member_data_service = MemberDataService()


@router.post("/all", response_model=ResponseModel)
async def get_all_member_data(query_params: QueryParams):
    """获取所有会员数据"""
    try:
        data = await member_data_service.get_all_member_data(query_params)
        return ResponseModel(
            code=200,
            message="获取会员数据成功",
            data=data.model_dump(by_alias=True) if data else None
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取会员数据异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/member-base", response_model=ResponseModel)
async def get_member_base_data(query_params: QueryParams):
    """获取会员基础数据（单独接口）"""
    try:
        data = await member_base_service.get_member_base_data(query_params)
        return ResponseModel(
            code=200,
            message="获取会员基础数据成功",
            data=data.model_dump(by_alias=True) if data else None
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取会员基础数据异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/member-consume", response_model=ResponseModel)
async def get_member_consume_data(query_params: QueryParams):
    """获取会员消费数据（单独接口）"""
    try:
        data = await member_consume_service.get_member_consume_data(query_params)
        return ResponseModel(
            code=200,
            message="获取会员消费数据成功",
            data=data.model_dump(by_alias=True) if data else None
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取会员消费数据异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/member-charge", response_model=ResponseModel)
async def get_member_charge_data(query_params: QueryParams):
    """获取会员充值数据（单独接口）"""
    try:
        data = await member_charge_service.get_member_charge_data(query_params)
        
        # 添加调试日志
        logger.info(f"会员充值数据原始对象: {data}")
        
        # 转换为字典格式
        data_dict = data.model_dump(by_alias=True) if data else None
        logger.info(f"会员充值数据转换后: {data_dict}")
        
        return ResponseModel(
            code=200,
            message="获取会员充值数据成功",
            data=data_dict
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取会员充值数据异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/coupon-trade", response_model=ResponseModel)
async def get_coupon_trade_data(query_params: QueryParams):
    """获取券交易数据（单独接口）"""
    try:
        coupon_trade_service = await member_data_service.get_coupon_trade_service()
        result = await coupon_trade_service.get_coupon_trade_data(query_params)
        return result
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取券交易数据异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/coupon-trade/ai-analysis", response_model=ResponseModel)
async def get_coupon_trade_ai_analysis(query_params: QueryParams):
    """获取券交易AI分析（单独接口）"""
    try:
        coupon_trade_service = await member_data_service.get_coupon_trade_service()
        result = await coupon_trade_service.get_ai_analysis(query_params)
        return result
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取券交易AI分析异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/member-base/ai-analysis", response_model=ResponseModel)
async def get_member_base_ai_analysis(query_params: QueryParams):
    """获取会员基础AI分析（单独接口）"""
    try:
        # 先获取数据
        data_result = await member_base_service.get_member_base_data(query_params)
        logger.info(f"会员基础数据获取成功: {data_result}")
        
        # 准备查询参数
        query_params_dict = {
            "query_type": query_params.query_type,
            "bid": query_params.bid,
            "sid": query_params.sid,
            "start_date": query_params.start_date,
            "end_date": query_params.end_date
        }
        
        # 调用AI分析服务
        analysis_result = await ai_analysis_service.analyze_member_base_data(
            data_result, query_params_dict
        )
        
        return ResponseModel(
            code=200,
            message="获取会员基础AI分析成功",
            data=analysis_result
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取会员基础AI分析异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/member-consume/ai-analysis", response_model=ResponseModel)
async def get_member_consume_ai_analysis(query_params: QueryParams):
    """获取会员消费AI分析（单独接口）"""
    try:
        # 先获取数据
        data_result = await member_consume_service.get_member_consume_data(query_params)
        logger.info(f"会员消费数据获取成功: {data_result}")
        
        # 准备查询参数
        query_params_dict = {
            "query_type": query_params.query_type,
            "bid": query_params.bid,
            "sid": query_params.sid,
            "start_date": query_params.start_date,
            "end_date": query_params.end_date
        }
        
        # 调用AI分析服务
        analysis_result = await ai_analysis_service.analyze_member_consume_data(
            data_result, query_params_dict
        )
        
        return ResponseModel(
            code=200,
            message="获取会员消费AI分析成功",
            data=analysis_result
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取会员消费AI分析异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/member-charge/ai-analysis", response_model=ResponseModel)
async def get_member_charge_ai_analysis(query_params: QueryParams):
    """获取会员充值AI分析（单独接口）"""
    try:
        # 先获取数据
        data_result = await member_charge_service.get_member_charge_data(query_params)
        logger.info(f"会员充值数据获取成功: {data_result}")
        
        # 准备查询参数
        query_params_dict = {
            "query_type": query_params.query_type,
            "bid": query_params.bid,
            "sid": query_params.sid,
            "start_date": query_params.start_date,
            "end_date": query_params.end_date
        }
        
        # 调用AI分析服务
        analysis_result = await ai_analysis_service.analyze_member_charge_data(
            data_result, query_params_dict
        )
        
        return ResponseModel(
            code=200,
            message="获取会员充值AI分析成功",
            data=analysis_result
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取会员充值AI分析异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/pinzhi-cashier", response_model=ResponseModel)
async def get_pinzhi_cashier_data(query_params: QueryParams):
    """获取品智收银数据（单独接口）"""
    try:
        logger.info(f"=== 品智收银API调用开始 ===")
        logger.info(f"接收到的查询参数: {query_params}")
        logger.info(f"查询参数类型: {type(query_params)}")
        logger.info(f"收银系统: {getattr(query_params, 'cashier_system', '未设置')}")
        logger.info(f"商户ID: {getattr(query_params, 'merchant_id', '未设置')}")

        # 🔥 新增：API层品智收银品牌验证
        if hasattr(query_params, 'cashier_system') and query_params.cashier_system == '1':
            if not hasattr(query_params, 'bid') or not query_params.bid:
                logger.error("品智收银系统需要提供品牌ID")
                raise HTTPException(status_code=400, detail="品智收银系统需要提供品牌ID")

            if not validate_bid_for_pinzhi(query_params.bid):
                error_message = get_validation_error_message(query_params.bid)
                logger.error(f"API层验证失败: {error_message}")
                raise HTTPException(status_code=400, detail=error_message)

            logger.info(f"API层品智收银验证通过，bid: {query_params.bid}")

        # 调用品智收银服务
        data_result = await pinzhi_cashier_service.get_pinzhi_cashier_data(query_params)
        logger.info(f"品智收银服务返回的原始数据: {data_result}")
        logger.info(f"返回数据类型: {type(data_result)}")
        logger.info(f"返回数据长度: {len(data_result) if isinstance(data_result, dict) else '非字典类型'}")

        # 详细记录每个字段的值
        if isinstance(data_result, dict):
            for key, value in data_result.items():
                logger.info(f"字段 '{key}': {value} (类型: {type(value)})")

        response_data = ResponseModel(
            code=200,
            message="获取品智收银数据成功",
            data=data_result
        )

        logger.info(f"最终API响应: {response_data}")
        logger.info(f"=== 品智收银API调用结束 ===")

        return response_data
    except HTTPException as e:
        logger.error(f"品智收银API HTTPException: {e}")
        raise e
    except Exception as e:
        logger.error(f"获取品智收银数据异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/pinzhi-cashier-ai-analysis", response_model=ResponseModel)
async def get_pinzhi_cashier_ai_analysis(query_params: QueryParams):
    """获取品智收银AI分析（单独接口）"""
    try:
        # 先获取数据
        data_result = await pinzhi_cashier_service.get_pinzhi_cashier_data(query_params)
        logger.info(f"品智收银数据获取成功: {data_result}")

        # 准备查询参数
        query_params_dict = {
            "query_type": query_params.query_type,
            "bid": query_params.bid,
            "sid": query_params.sid,
            "start_date": query_params.start_date,
            "end_date": query_params.end_date,
            "cashier_system": getattr(query_params, 'cashier_system', '1'),
            "merchant_id": getattr(query_params, 'merchant_id', None)
        }

        # 调用AI分析服务
        analysis_result = await ai_analysis_service.analyze_pinzhi_cashier_data(
            data_result, query_params_dict
        )

        return ResponseModel(
            code=200,
            message="获取品智收银AI分析成功",
            data=analysis_result
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取品智收银AI分析异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
