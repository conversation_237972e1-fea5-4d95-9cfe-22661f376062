/**
 * 品智收银门店映射配置
 * 数据来源：member-api\api\query\PinzhiOrder\StoreList.py
 */

// 品智门店映射字典 - 拼音名称到中文显示名称的映射
export const PINZHI_STORE_MAPPING = {
  bianyifang: '便宜坊',
  chaimisichu: '柴米私厨',
  chujishaoe: '褚记烤鸭',
  dafeng<PERSON><PERSON>: '大丰收',
  guangshunxing: '广顺兴',
  huolupangkaorou: '火炉胖烤肉',
  jiaheyipin: '嘉和一品',
  jiangbianchengwai: '江边城外',
  jiaodongxiaoguan: '胶东小馆',
  majialalie: '玛嘉烈',
  quchaqu: '去茶去',
  rongyu: '蓉语串串火锅',
  shunliu_new: '顺溜削面',
  songzirilao: '松子日料',
  taixing: '太兴',
  wangchunchun: '王春春鸡汤饭',
  wangshunge: '旺顺阁鱼头泡饼',
  xianglala: '湘辣辣',
  xiaocaiyuan: '小菜园',
  xia<PERSON><PERSON><PERSON>: '小柴米',
  yun<PERSON><PERSON><PERSON>: '云海肴',
  yushihu: '渝是乎',
  yuyuyu: '渔语鱼酸菜鱼'
}

// 🔥 新增：品智收银品牌bid映射表
// 包含支持品智收银的品牌及其对应的bid
export const PINZHI_BRAND_BID_MAPPING = {
  'chujishaoe': {
    name: '褚记烤鸭',
    bid: '1705675720'
  },
  'huolupangkaorou': {
    name: '火炉胖烤肉',
    bid: '1888658507'
  },
  'jiaheyipin': {
    name: '嘉和一品',
    bid: '1880504772'
  },
  'jiangbianchengwai': {
    name: '江边城外',
    bid: '1228700339'
  },
  'quchaqu': {
    name: '去茶去',
    bid: '3599689981'
  },
  'rongyu': {
    name: '蓉语串串火锅',
    bid: '1055020055'
  },
  'shunliu_new': {
    name: '顺溜削面',
    bid: '2235708661'
  },
  'wangchunchun': {
    name: '王春春鸡汤饭',
    bid: '3578315180'
  },
  'wangshunge': {
    name: '旺顺阁鱼头泡饼',
    bid: '1307425561'
  },
  'xianglala': {
    name: '湘辣辣',
    bid: '1703248138'
  },
  'xiaocaiyuan': {
    name: '小菜园',
    bid: '2926758168'
  },
  'xiaochaimi': {
    name: '小柴米',
    bid: '1235363899'
  }
}

// 🔥 新增：创建bid到品牌信息的反向映射
export const BID_TO_BRAND_MAPPING = Object.fromEntries(
  Object.entries(PINZHI_BRAND_BID_MAPPING).map(([pinyinName, brandInfo]) => [
    brandInfo.bid,
    {
      pinyinName,
      chineseName: brandInfo.name
    }
  ])
)

/**
 * 获取门店选项列表，用于下拉框显示
 * @returns {Array} 门店选项数组
 */
export function getStoreOptions() {
  return Object.entries(PINZHI_STORE_MAPPING).map(([key, value]) => ({
    label: value,  // 中文显示名称
    value: key     // 拼音名称作为值
  }))
}

/**
 * 根据拼音名称获取中文显示名称
 * @param {string} pinyinName - 拼音名称
 * @returns {string} 中文显示名称
 */
export function getStoreDisplayName(pinyinName) {
  return PINZHI_STORE_MAPPING[pinyinName] || pinyinName
}

/**
 * 根据中文显示名称获取拼音名称
 * @param {string} displayName - 中文显示名称
 * @returns {string} 拼音名称
 */
export function getStorePinyinName(displayName) {
  const entry = Object.entries(PINZHI_STORE_MAPPING).find(([key, value]) => value === displayName)
  return entry ? entry[0] : displayName
}

/**
 * 获取门店总数
 * @returns {number} 门店数量
 */
export function getStoreCount() {
  return Object.keys(PINZHI_STORE_MAPPING).length
}

// 🔥 新增：品智收银验证函数

/**
 * 验证给定的bid是否可以使用品智收银系统
 * @param {string} bid - 品牌ID
 * @returns {boolean} true表示可以使用品智收银，false表示不可以
 */
export function validateBidForPinzhi(bid) {
  if (!bid) {
    return false
  }
  return bid in BID_TO_BRAND_MAPPING
}

/**
 * 根据bid获取品牌信息
 * @param {string} bid - 品牌ID
 * @returns {Object|null} 品牌信息对象，包含拼音名称和中文名称，如果不存在返回null
 */
export function getBrandInfoByBid(bid) {
  return BID_TO_BRAND_MAPPING[bid] || null
}

/**
 * 获取所有支持品智收银的品牌列表
 * @returns {Array} 支持品智收银的品牌信息数组
 */
export function getSupportedPinzhiBrands() {
  return Object.entries(PINZHI_BRAND_BID_MAPPING).map(([pinyinName, brandInfo]) => ({
    pinyinName,
    chineseName: brandInfo.name,
    bid: brandInfo.bid
  }))
}

/**
 * 获取bid验证失败时的错误信息
 * @param {string} bid - 品牌ID
 * @returns {string} 详细的错误信息
 */
export function getValidationErrorMessage(bid) {
  const supportedBrands = getSupportedPinzhiBrands()
    .map(brand => `${brand.chineseName}(bid:${brand.bid})`)
    .join('、')

  return `品牌ID ${bid} 不支持品智收银系统。支持品智收银的品牌包括：${supportedBrands}。请确认您的品牌ID是否正确，或选择"0-无收银系统"选项。`
}

/**
 * 检查bid是否为支持品智收银的品牌
 * @param {string} bid - 品牌ID
 * @returns {Object} 验证结果对象 { isValid: boolean, brandInfo: Object|null, errorMessage: string }
 */
export function checkBidForPinzhi(bid) {
  const isValid = validateBidForPinzhi(bid)
  const brandInfo = getBrandInfoByBid(bid)
  const errorMessage = isValid ? '' : getValidationErrorMessage(bid)

  return {
    isValid,
    brandInfo,
    errorMessage
  }
}