<template>
  <div class="member-data-query">
    <!-- 查询表单 -->
    <el-card class="query-form-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>
            <el-icon><Search /></el-icon>
            会员数据查询
          </h3>
        </div>
      </template>
      
      <el-form ref="queryForm" :model="queryParams" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="查询类型" prop="queryType">
              <el-select v-model="queryParams.queryType" placeholder="请选择查询类型" @change="handleQueryTypeChange">
                <el-option label="周分析" value="week" />
                <el-option label="月分析" value="month" />
                <el-option label="季度分析" value="quarter" />
                <el-option label="半年分析" value="halfyear" />
                <el-option label="自定义查询" value="custom" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="品牌ID" prop="bid">
              <el-input v-model="queryParams.bid" placeholder="请输入品牌ID（必填）" />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="门店ID" prop="sid">
              <el-input v-model="queryParams.sid" placeholder="请输入门店ID（选填）" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="开始日期" prop="startDate">
              <el-date-picker
                v-model="queryParams.startDate"
                type="date"
                placeholder="请选择开始日期"
                :disabled-date="disabledDate"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="8" v-if="queryParams.queryType === 'custom'">
            <el-form-item label="结束日期" prop="endDate">
              <el-date-picker
                v-model="queryParams.endDate"
                type="date"
                placeholder="请选择结束日期"
                :disabled-date="disabledEndDate"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第三行：品智收银筛选条件 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="收银系统" prop="cashierSystem">
              <el-select
                v-model="queryParams.cashierSystem"
                placeholder="请选择收银系统"
                @change="handleCashierSystemChange"
              >
                <el-option label="0-无收银系统" value="0" />
                <el-option
                  label="1-品智收银"
                  value="1"
                  :disabled="!canSelectPinzhi"
                />
              </el-select>
              <!-- 🔥 新增：品智收银验证提示 -->
              <div v-if="queryParams.bid && !canSelectPinzhi" class="validation-tip">
                <el-text type="warning" size="small">
                  当前品牌ID不支持品智收银系统
                </el-text>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="8" v-if="queryParams.cashierSystem === '1'">
            <el-form-item label="商户选择" prop="merchantId">
              <el-select
                v-model="queryParams.merchantId"
                placeholder="请选择商户"
                filterable
                clearable
              >
                <!-- 🔥 修改：使用过滤后的门店选项，只显示与当前bid匹配的商户 -->
                <el-option
                  v-for="store in filteredStoreOptions"
                  :key="store.value"
                  :label="store.label"
                  :value="store.value"
                />
              </el-select>
              <!-- 🔥 新增：当没有匹配的商户时显示提示 -->
              <div v-if="queryParams.bid && filteredStoreOptions.length === 0" class="validation-tip">
                <el-text type="warning" size="small">
                  当前品牌ID没有对应的品智收银商户
                </el-text>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item>
              <el-button type="primary" @click="handleQuery" :loading="loading">
                <el-icon><Search /></el-icon>
                开始查询
              </el-button>
              <el-button @click="handleReset">
                <el-icon><RefreshRight /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <!-- 日期限制提示 -->
      <div v-if="dateLimit" class="date-limit-tip">
        <el-alert :title="dateLimit.label" type="info" :closable="false" show-icon />
      </div>
    </el-card>

    <!-- 查询结果 -->
    <div v-if="hasData" class="query-results">
      <el-card shadow="never">
        <template #header>
          <div class="card-header">
            <h3>
              <el-icon><DataLine /></el-icon>
              查询结果
            </h3>
            <div class="query-info">
              <el-tag type="info">{{ queryTypeLabel }}</el-tag>
              <el-tag type="success">{{ formatDate(queryParams.startDate) }} 开始</el-tag>
              <el-tag v-if="queryParams.endDate" type="warning">{{ formatDate(queryParams.endDate) }} 结束</el-tag>
            </div>
          </div>
        </template>
        
        <!-- 数据展示Tab -->
        <el-tabs v-model="activeTab" class="data-tabs">
          <el-tab-pane label="会员基础信息" name="memberBase">
            <MemberBaseTab :query-params="currentQueryParams" @update-loading="updateLoading" />
          </el-tab-pane>
          
          <el-tab-pane label="会员消费信息" name="memberConsume">
            <MemberConsumeTab :query-params="currentQueryParams" @update-loading="updateLoading" />
          </el-tab-pane>
          
          <el-tab-pane label="会员充值信息" name="memberCharge">
            <MemberChargeTab :query-params="currentQueryParams" @update-loading="updateLoading" />
          </el-tab-pane>
          
          <el-tab-pane label="券交易信息" name="couponTrade">
            <CouponTradeTab :query-params="currentQueryParams" @update-loading="updateLoading" />
          </el-tab-pane>

          <el-tab-pane label="品智收银信息" name="pinzhiCashier" v-if="queryParams.cashierSystem === '1' && hasData">
            <PinzhiTab :query-params="currentQueryParams" @update-loading="updateLoading" />
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
    
    <!-- 空状态 -->
    <el-empty
      v-else
      :image-size="200"
      description="请先选择查询条件并点击<开始查询>按钮"
    >
      <template #image>
        <el-icon size="200" color="#c0c4cc">
          <Search />
        </el-icon>
      </template>
    </el-empty>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, RefreshRight, DataLine } from '@element-plus/icons-vue'
import SampleData, { SampleDataGenerator } from '@/data/sampleData'
import { getStoreOptions, validateBidForPinzhi, checkBidForPinzhi, getBrandInfoByBid } from '@/utils/PinzhiStoreMapping'
import MemberBaseTab from './tabs/MemberBaseTab.vue'
import MemberConsumeTab from './tabs/MemberConsumeTab.vue'
import MemberChargeTab from './tabs/MemberChargeTab.vue'
import CouponTradeTab from './tabs/CouponTradeTab.vue'
import PinzhiTab from './tabs/PinzhiTab.vue'

export default {
  name: 'MemberDataQuery',
  components: {
    MemberBaseTab,
    MemberConsumeTab,
    MemberChargeTab,
    CouponTradeTab,
    PinzhiTab
  },
  setup() {
    const loading = ref(false)
    const hasData = ref(false)
    const activeTab = ref('memberBase')
    const dateLimit = ref(null)
    
    // 查询参数
    const queryParams = reactive({
      queryType: 'week',
      bid: '',
      sid: '',
      startDate: null,
      endDate: null,
      cashierSystem: '0',  // 默认无收银系统
      merchantId: ''       // 商户ID
    })

    // 门店选项
    const storeOptions = ref(getStoreOptions())

    // 🔥 新增：品智收银验证计算属性
    const canSelectPinzhi = computed(() => {
      if (!queryParams.bid) {
        return false  // 没有输入bid时不能选择品智收银
      }
      return validateBidForPinzhi(queryParams.bid)
    })

    // 🔥 新增：过滤后的门店选项（只显示与当前bid匹配的商户）
    const filteredStoreOptions = computed(() => {
      if (!queryParams.bid || queryParams.cashierSystem !== '1') {
        return storeOptions.value  // 非品智收银模式时显示所有选项
      }

      const brandInfo = getBrandInfoByBid(queryParams.bid)
      if (!brandInfo) {
        return []  // 如果bid无效，不显示任何选项
      }

      // 只返回与当前bid匹配的商户选项
      return storeOptions.value.filter(store => store.value === brandInfo.pinyinName)
    })

    // 🔥 新增：监听bid变化，自动选择对应的品智品牌
    watch(() => queryParams.bid, (newBid) => {
      if (newBid && validateBidForPinzhi(newBid)) {
        // 如果输入的bid支持品智收银，自动选择品智收银并设置对应的商户
        const brandInfo = getBrandInfoByBid(newBid)
        if (brandInfo) {
          queryParams.cashierSystem = '1'  // 自动选择品智收银
          queryParams.merchantId = brandInfo.pinyinName  // 自动选择对应的商户
          console.log(`自动选择品智收银: ${brandInfo.chineseName} (${brandInfo.pinyinName})`)
        }
      } else if (newBid && !validateBidForPinzhi(newBid)) {
        // 如果输入的bid不支持品智收银，自动切换到无收银系统
        if (queryParams.cashierSystem === '1') {
          queryParams.cashierSystem = '0'
          queryParams.merchantId = ''
          console.log('bid不支持品智收银，自动切换到无收银系统')
        }
      }
    })

    // 当前查询参数（用于传递给子组件）
    const currentQueryParams = ref(null)
    
    // 数据存储
    const memberBaseData = ref(null)
    const memberConsumeData = ref(null)
    const memberChargeData = ref(null)
    const couponTradeData = ref(null)
    
    // 表单验证规则
    const rules = {
      queryType: [
        { required: true, message: '请选择查询类型', trigger: 'change' }
      ],
      bid: [
        { required: true, message: '请输入品牌ID', trigger: 'blur' }
      ],
      startDate: [
        { required: true, message: '请选择开始日期', trigger: 'change' }
      ]
    }
    
    // 查询类型标签
    const queryTypeLabel = computed(() => {
      const labels = {
        week: '周分析',
        month: '月分析',
        quarter: '季度分析',
        halfyear: '半年分析',
        custom: '自定义查询'
      }
      return labels[queryParams.queryType] || '未知类型'
    })
    
    // 更新loading状态
    const updateLoading = (value) => {
      loading.value = value
    }
    
    // 日期禁用逻辑
    const disabledDate = (time) => {
      if (!queryParams.queryType) return true
      
      const limit = SampleData.dateLimits(queryParams.queryType)
      return time.getTime() > limit.maxDate.getTime() || time.getTime() < limit.minDate.getTime()
    }
    
    // 结束日期禁用逻辑
    const disabledEndDate = (time) => {
      if (!queryParams.startDate) return true
      return time.getTime() < queryParams.startDate.getTime()
    }
    
    // 处理查询类型变化
    const handleQueryTypeChange = (value) => {
      queryParams.startDate = null
      queryParams.endDate = null

      // 更新日期限制
      dateLimit.value = SampleData.dateLimits(value)
    }

    // 处理收银系统变化
    const handleCashierSystemChange = (value) => {
      // 当切换收银系统时，清空商户选择
      queryParams.merchantId = ''

      // 如果切换到品智收银，可以在这里做一些初始化操作
      if (value === '1') {
        console.log('切换到品智收银系统')
      } else {
        console.log('切换到无收银系统')
      }
    }
    
    // 处理查询
    const handleQuery = async () => {
      console.log('=== 开始查询 ===')
      console.log('查询参数:', queryParams)
      
      // 验证必填字段
      if (!queryParams.bid) {
        console.log('验证失败: 缺少品牌ID')
        ElMessage.error('请输入品牌ID')
        return
      }
      
      if (!queryParams.startDate) {
        console.log('验证失败: 缺少开始日期')
        ElMessage.error('请选择开始日期')
        return
      }
      
      // 品智收银系统验证
      if (queryParams.cashierSystem === '1') {
        // 🔥 新增：验证bid是否支持品智收银
        const bidValidation = checkBidForPinzhi(queryParams.bid)
        if (!bidValidation.isValid) {
          console.log('验证失败: bid不支持品智收银系统')
          ElMessage.error(bidValidation.errorMessage)
          return
        }

        if (!queryParams.merchantId) {
          console.log('验证失败: 选择品智收银时必须选择商户')
          ElMessage.error('选择品智收银时必须选择商户')
          return
        }

        console.log(`品智收银验证通过: ${bidValidation.brandInfo.chineseName}`)
      }

      console.log('验证通过，开始构建查询数据')
      loading.value = true

      try {
        // 构建查询参数
        const queryData = {
          query_type: queryParams.queryType,
          bid: queryParams.bid,
          sid: queryParams.sid || null,
          start_date: queryParams.startDate ? formatDateForAPI(queryParams.startDate) : null,
          end_date: queryParams.endDate ? formatDateForAPI(queryParams.endDate) : null,
          compare_options: ['chain', 'yearOnYear'], // 默认启用环比和同比
          cashier_system: queryParams.cashierSystem || '0',
          merchant_id: queryParams.merchantId || null
        }
        
        // 如果不是自定义查询，根据查询类型计算结束日期
        if (queryParams.queryType !== 'custom') {
          const startDate = new Date(queryParams.startDate)
          let endDate = new Date(startDate)
          
          switch (queryParams.queryType) {
            case 'week':
              // 周：从选择的日期开始，持续7天
              endDate.setDate(startDate.getDate() + 6)
              break
            case 'month':
              // 月：从选择的日期开始，到下个月的同一天的前一天
              endDate.setMonth(startDate.getMonth() + 1)
              endDate.setDate(startDate.getDate() - 1)
              break
            case 'quarter':
              // 季度：从选择的日期开始，到第3个月后的同一天的前一天
              endDate.setMonth(startDate.getMonth() + 3)
              endDate.setDate(startDate.getDate() - 1)
              break
            case 'halfyear':
              // 半年：从选择的日期开始，到第6个月后的同一天的前一天
              endDate.setMonth(startDate.getMonth() + 6)
              endDate.setDate(startDate.getDate() - 1)
              break
          }
          
          queryData.end_date = formatDateForAPI(endDate)
        }
        
        console.log('构建的查询数据:', queryData)
        
        // 设置当前查询参数
        currentQueryParams.value = queryData
        
        // 为其他tab生成样本数据（暂时保留）
        memberConsumeData.value = SampleDataGenerator.generateMemberConsumeData(queryParams.queryType)
        memberChargeData.value = SampleDataGenerator.generateMemberChargeData(queryParams.queryType)
        couponTradeData.value = SampleDataGenerator.generateCouponTradeData()
        
        hasData.value = true
        console.log('查询参数设置完成，数据区域已显示')
        ElMessage.success('查询参数已设置，正在获取数据...')
        
      } catch (error) {
        console.error('查询失败:', error)
        ElMessage.error('查询失败：' + error.message)
      } finally {
        loading.value = false
        console.log('=== 查询结束 ===')
      }
    }
    
    // 重置表单
    const handleReset = () => {
      queryParams.queryType = 'week'
      queryParams.bid = ''
      queryParams.sid = ''
      queryParams.startDate = null
      queryParams.endDate = null
      queryParams.cashierSystem = '0'
      queryParams.merchantId = ''
      currentQueryParams.value = null
      hasData.value = false
      dateLimit.value = null
    }
    
    // 格式化日期
    const formatDate = (date) => {
      if (!date) return ''
      return new Date(date).toLocaleDateString('zh-CN')
    }

    // 格式化日期为API所需格式 (YYYY-MM-DD)
    const formatDateForAPI = (date) => {
      if (!date) return null
      
      // 避免时区转换问题，使用本地时间
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      
      return `${year}-${month}-${day}`
    }
    
    // 初始化
    onMounted(() => {
      handleQueryTypeChange('week')
    })
    
    return {
      loading,
      hasData,
      activeTab,
      queryParams,
      currentQueryParams,
      rules,
      dateLimit,
      storeOptions,
      filteredStoreOptions,  // 🔥 新增：过滤后的门店选项
      canSelectPinzhi,  // 🔥 新增：品智收银验证计算属性
      memberBaseData,
      memberConsumeData,
      memberChargeData,
      couponTradeData,
      queryTypeLabel,
      updateLoading,
      disabledDate,
      disabledEndDate,
      handleQueryTypeChange,
      handleCashierSystemChange,
      handleQuery,
      handleReset,
      formatDate,
      Search,
      RefreshRight,
      DataLine
    }
  }
}
</script>

<style scoped>
.member-data-query {
  padding: 20px;
  min-height: 100vh;
  overflow: auto;
  width: 100%;
}

.query-form-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
}

.query-info {
  display: flex;
  gap: 8px;
}

.date-limit-tip {
  margin-top: 16px;
}

.query-results {
  margin-top: 20px;
  width: 100%;
}

.data-tabs {
  margin-top: 20px;
}

.data-tabs :deep(.el-tabs__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 0;
}

.data-tabs :deep(.el-tabs__nav) {
  background: transparent;
}

.data-tabs :deep(.el-tabs__item) {
  color: rgba(255, 255, 255, 0.8);
  border-color: rgba(255, 255, 255, 0.2);
}

.data-tabs :deep(.el-tabs__item.is-active) {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

.data-tabs :deep(.el-tabs__content) {
  padding: 20px;
  border-top: none;
  border-left: none;
  border-right: none;
  border-bottom: none;
}

/* 确保图表容器有足够的宽度 */
.data-tabs :deep(.el-tab-pane) {
  width: 100%;
  min-width: 800px;
}

/* 确保图表行有足够的空间 */
.data-tabs :deep(.chart-section) {
  width: 100%;
  margin: 0;
  padding: 0 10px;
}

/* 确保图表列有足够的空间 */
.data-tabs :deep(.chart-section .el-col) {
  padding: 0 10px;
}

/* 确保图表卡片有足够的空间 */
.data-tabs :deep(.chart-card) {
  width: 100%;
  margin-bottom: 20px;
}

/* 🔥 新增：品智收银验证提示样式 */
.validation-tip {
  margin-top: 4px;
  font-size: 12px;
}
</style>