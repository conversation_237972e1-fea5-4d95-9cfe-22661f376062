"""
会员充值数据SQL查询模块
将每个SQL查询拆分成独立的函数，便于维护和独立管理
"""

from typing import Optional
import logging

logger = logging.getLogger(__name__)

class MemberChargeSqlQueries:
    """会员充值数据SQL查询类 - 每个查询都是独立的函数"""
    
    # ========== dwoutput数据库充值基础数据查询 ==========
    
    @staticmethod
    def get_dwoutput_total_charge_pv_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取充值笔数查询SQL
        
        数据库：dprpt_welife_charge_log_bid
        字段：charge_amount_pv（充值笔数）, cancel_charge_amount_pv（取消充值笔数）
        计算方式：时间范围内所有天的(charge_amount_pv - cancel_charge_amount_pv)求和
        说明：充值笔数 = 实际充值笔数 - 取消充值笔数
        
        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)
        
        Returns:
            充值笔数查询SQL
        """
        return f"""
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        """
    
    @staticmethod
    def get_dwoutput_total_charge_cash_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取实收充值金额查询SQL
        
        数据库：dprpt_welife_charge_log_bid
        字段：charge_cash（充值现金金额）, cancel_charge_cash（取消充值现金金额）
        计算方式：时间范围内所有天的(charge_cash - cancel_charge_cash)求和
        说明：实收充值金额 = 实际充值金额 - 取消充值金额
        注意：数据库存储的是真实值*100，需要在业务层除以100
        
        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)
        
        Returns:
            实收充值金额查询SQL
        """
        return f"""
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        """
    
    @staticmethod
    def get_dwoutput_total_charge_amount_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取期间充值金额查询SQL
        
        数据库：dprpt_welife_charge_log_bid
        字段：charge_amount（充值金额）, cancel_charge_amount（取消充值金额）
        计算方式：时间范围内所有天的(charge_amount - cancel_charge_amount)求和
        说明：期间充值金额 = 实际充值金额 - 取消充值金额
        注意：数据库存储的是真实值*100，需要在业务层除以100
        """
        return f"""
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        """
    
    @staticmethod
    def get_dwoutput_total_charge_present_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取期间充值赠送金额查询SQL
        
        数据库：dprpt_welife_charge_log_bid
        字段：charge_present（充值赠送金额）, cancel_charge_present（取消充值赠送金额）
        计算方式：时间范围内所有天的(charge_present - cancel_charge_present)求和
        说明：期间充值赠送金额 = 实际充值赠送金额 - 取消充值赠送金额
        注意：数据库存储的是真实值*100，需要在业务层除以100
        """
        return f"""
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        """
    
    @staticmethod
    def get_dwoutput_charge_amount_unused_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取期末储值沉淀金额查询SQL
        
        数据库：dprpt_welife_charge_log_bid
        字段：charge_amount_unused（储值沉淀金额）
        计算方式：最后一天的charge_amount_unused求和
        说明：期末储值沉淀金额 = 最后一天的储值沉淀金额
        注意：数据库存储的是真实值*100，需要在业务层除以100
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        
        return f"""
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = {end_date}
         AND bid = {bid}
         {sid_condition}) AS total_charge_amount_unused
        """

    @staticmethod
    def get_dwoutput_total_consume_prepay_used_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取储值实收消耗金额查询SQL - 新逻辑
        
        计算方式：前一天沉淀金额 - 最后一天沉淀金额 + 期间实收净充值
        说明：通过沉淀金额变化和期间充值来计算实际消耗
        注意：数据库存储的是真实值*100，需要在业务层除以100
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        
        # 计算前一天日期
        from datetime import datetime, timedelta
        start_dt = datetime.strptime(start_date, "%Y%m%d")
        prev_date = (start_dt - timedelta(days=1)).strftime("%Y%m%d")
        
        return f"""
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = {prev_date} AND bid = {bid} {sid_condition})
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = {end_date} AND bid = {bid} {sid_condition})
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN {start_date} AND {end_date} AND bid = {bid} {sid_condition})
        ) AS total_consume_prepay_used
        """

    # ========== 组合查询构建函数 ==========
    
    @staticmethod
    def build_dwoutput_charge_base_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """构建dwoutput数据库的充值基础数据查询SQL"""
        sid_condition = f"AND sid = '{sid}'" if sid else ""
        
        sql = f"""
        SELECT
          {MemberChargeSqlQueries.get_dwoutput_total_charge_pv_sql(start_date, end_date, bid, sid)},
          {MemberChargeSqlQueries.get_dwoutput_total_charge_cash_sql(start_date, end_date, bid, sid)},
          {MemberChargeSqlQueries.get_dwoutput_total_charge_amount_sql(start_date, end_date, bid, sid)},
          {MemberChargeSqlQueries.get_dwoutput_total_charge_present_sql(start_date, end_date, bid, sid)},
          {MemberChargeSqlQueries.get_dwoutput_charge_amount_unused_sql(start_date, end_date, bid, sid)}
        FROM dprpt_welife_charge_log_bid
        WHERE ftime BETWEEN {start_date} AND {end_date}
          AND bid = {bid}
          {sid_condition}
        """
        
        logger.debug(f"构建dwoutput充值基础查询SQL: bid={bid}, sid={sid}, date_range={start_date}-{end_date}")
        return sql
    



class MemberChargeCalculator:
    """会员充值数据计算器"""
    
    @staticmethod
    def calculate_prepay_retention_rate(total_charge_cash: float, total_consume_prepay_used: float) -> float:
        """计算储值留存率

        计算公式：储值留存率 = (实收充值金额 - 储值消耗金额) / |实收充值金额| * 100

        Args:
            total_charge_cash: 实收充值金额 (来自dprpt_welife_charge_log_bid.charge_cash - cancel_charge_cash)
            total_consume_prepay_used: 储值消耗金额 (来自dprpt_welife_trade_consume_detail.tclprinciple)

        Returns:
            储值留存率（百分比数值，如19.0表示19%）
        """
        try:
            # 确保数据类型转换为float
            total_charge_cash = float(total_charge_cash) if total_charge_cash is not None else 0.0
            total_consume_prepay_used = float(total_consume_prepay_used) if total_consume_prepay_used is not None else 0.0

            if total_charge_cash == 0:
                logger.warning("实收充值金额为0，储值留存率无法计算")
                return 0.0

            # 计算留存金额（充值金额 - 消耗金额）
            retention_amount = total_charge_cash - total_consume_prepay_used

            # 计算留存率（留存金额 / 充值金额的绝对值）并转换为百分比
            retention_rate = (retention_amount / abs(total_charge_cash)) * 100
            result = round(retention_rate, 2)
            logger.debug(f"储值留存率计算: ({total_charge_cash} - {total_consume_prepay_used}) / {total_charge_cash} * 100 = {result}%")
            return result

        except Exception as e:
            logger.error(f"计算储值留存率失败: {str(e)}")
            return 0.0
    
    @staticmethod
    def merge_charge_data(base_data: dict, detail_data: dict) -> dict:
        """合并充值数据"""
        try:
            logger.debug("开始合并充值数据")
            
            result = base_data.copy()
            
            # 添加储值消耗详情
            total_consume_prepay_used = detail_data.get('total_consume_prepay_used', 0) or 0
            total_consume_prepay_used = float(total_consume_prepay_used) if total_consume_prepay_used is not None else 0.0
            result['total_consume_prepay_used'] = total_consume_prepay_used
            
            # 计算储值留存率
            total_charge_cash = result.get('total_charge_cash', 0) or 0
            total_charge_cash = float(total_charge_cash) if total_charge_cash is not None else 0.0
            prepay_retention_rate = MemberChargeCalculator.calculate_prepay_retention_rate(
                total_charge_cash, total_consume_prepay_used
            )
            result['prepay_retention_rate'] = prepay_retention_rate
            
            logger.debug(f"充值数据合并完成: {len(result)} 个字段")
            return result
            
        except Exception as e:
            logger.error(f"合并充值数据失败: {str(e)}")
            return base_data
    
    @staticmethod
    def process_money_fields(data: dict, money_fields: list) -> dict:
        """处理金额字段，将数据库存储的值除以100
        
        Args:
            data: 原始数据字典
            money_fields: 需要处理的金额字段列表
        
        Returns:
            处理后的数据字典
        """
        result = data.copy()
        for field in money_fields:
            if field in result and result[field] is not None:
                try:
                    result[field] = float(result[field]) / 100.0
                except (ValueError, TypeError):
                    logger.warning(f"金额字段 {field} 转换失败，保持原值: {result[field]}")
                    result[field] = 0.0
        return result













