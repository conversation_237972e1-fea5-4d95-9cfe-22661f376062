"""
品智收银系统品牌验证常量
包含品牌bid映射关系和验证函数
"""

from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

# 品智收银品牌bid映射表
# 根据用户提供的映射关系，包含有bid的品牌
PINZHI_BRAND_BID_MAPPING = {
    'chujishaoe': {
        'name': '褚记烤鸭',
        'bid': '1705675720'
    },
    'huolupangkaorou': {
        'name': '火炉胖烤肉',
        'bid': '1888658507'
    },
    'jiaheyipin': {
        'name': '嘉和一品',
        'bid': '1880504772'
    },
    'jiangbianchengwai': {
        'name': '江边城外',
        'bid': '1228700339'
    },
    'quchaqu': {
        'name': '去茶去',
        'bid': '3599689981'
    },
    'rongyu': {
        'name': '蓉语串串火锅',
        'bid': '1055020055'
    },
    'shunliu_new': {
        'name': '顺溜削面',
        'bid': '2235708661'
    },
    'wangchunchun': {
        'name': '王春春鸡汤饭',
        'bid': '3578315180'
    },
    'wangshunge': {
        'name': '旺顺阁鱼头泡饼',
        'bid': '1307425561'
    },
    'xianglala': {
        'name': '湘辣辣',
        'bid': '1703248138'
    },
    'xiaocaiyuan': {
        'name': '小菜园',
        'bid': '2926758168'
    },
    'xiaochaimi': {
        'name': '小柴米',
        'bid': '1235363899'
    }
}

# 无品智收银系统的品牌列表
# 这些品牌没有对应的bid，不能选择品智收银
NON_PINZHI_BRANDS = [
    'bianyifang',      # 便宜坊
    'chaimisichu',     # 柴米私厨
    'dafengshou',      # 大丰收
    'guangshunxing',   # 广顺兴
    'jiaodongxiaoguan', # 胶东小馆
    'majialalie',      # 玛嘉烈
    'songzirilao',     # 松子日料
    'taixing',         # 太兴
    'yunhaiyao',       # 云海肴
    'yushihu',         # 渝是乎
    'yuyuyu'           # 渔语鱼酸菜鱼
]

# 创建bid到品牌信息的反向映射
BID_TO_BRAND_MAPPING = {
    brand_info['bid']: {
        'pinyin_name': pinyin_name,
        'chinese_name': brand_info['name']
    }
    for pinyin_name, brand_info in PINZHI_BRAND_BID_MAPPING.items()
}


def validate_bid_for_pinzhi(bid: str) -> bool:
    """
    验证给定的bid是否可以使用品智收银系统

    Args:
        bid: 品牌ID

    Returns:
        bool: True表示可以使用品智收银，False表示不可以
    """
    if not bid:
        return False

    # 检查bid是否在品智收银品牌映射中
    is_valid = bid in BID_TO_BRAND_MAPPING

    if is_valid:
        brand_info = BID_TO_BRAND_MAPPING[bid]
        logger.info(f"bid验证成功: {bid} -> {brand_info['chinese_name']} ({brand_info['pinyin_name']})")
    else:
        logger.warning(f"bid验证失败: {bid} 不在品智收银支持的品牌列表中")

    return is_valid


def get_brand_info_by_bid(bid: str) -> Optional[Dict[str, Any]]:
    """
    根据bid获取品牌信息

    Args:
        bid: 品牌ID

    Returns:
        Dict: 品牌信息字典，包含拼音名称和中文名称，如果不存在返回None
    """
    return BID_TO_BRAND_MAPPING.get(bid)


def get_supported_brands_info() -> Dict[str, Any]:
    """
    获取所有支持品智收银的品牌信息

    Returns:
        Dict: 包含支持的品牌列表和统计信息
    """
    return {
        'supported_brands': PINZHI_BRAND_BID_MAPPING,
        'non_supported_brands': NON_PINZHI_BRANDS,
        'total_supported': len(PINZHI_BRAND_BID_MAPPING),
        'total_non_supported': len(NON_PINZHI_BRANDS)
    }


def get_validation_error_message(bid: str) -> str:
    """
    获取bid验证失败时的错误信息

    Args:
        bid: 品牌ID

    Returns:
        str: 详细的错误信息
    """
    supported_brands = [f"{info['name']}(bid:{bid})"
                       for bid, info in BID_TO_BRAND_MAPPING.items()]

    return (f"品牌ID {bid} 不支持品智收银系统。"
            f"支持品智收银的品牌包括：{', '.join(supported_brands)}。"
            f"请确认您的品牌ID是否正确，或选择'0-无收银系统'选项。")