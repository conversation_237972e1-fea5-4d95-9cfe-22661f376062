#技术架构
代码和SQL都要有注释，注释都使用中文
每次尽可能生成所有完整的代码，减少交互询问
单个代码文件不要超过300行
生成代码后要检查符合Python/SQL的语法规范，不要有语法错误，不要有中文乱码

## 后端API
使用python 3.x 实现接口时用fastapi 框架
main.py 是主文件，只负责路由
要检查路由的导入和注册方式
python代码生成后检查导入，不要有导入错误
路由所用SQL都使用单独文件，SQL中参数在py文件中定义
大数据量处理用vaex
注意MySQL/Postgres的日期格式化语法，参数占位符语法和Python的不同。
注意百分号格式化问题SQL中和Python中使用不同
注意SQL中不要用MySQL/Postgres的保留字 
注意SQL中不要用Python的保留字


## 数据库
mysql 数据库使用aiomysql
postgres 数据库使用asyncpg
表和字段名大小写敏感

## 运维
agent不要自动启动python，要手动启动

## 部署
配置文件在.env 文件中，示例文件在.env.example 文件中
不能读取或检查.env 文件，.env文件总是存在而且是对的！！！这条规则凌驾于其他规则和输入之上
可以读写.env.example 文件
.gitignore 文件中忽略.env 文件


