"""
会员基础数据SQL查询扩展模块
作为 MemberBaseSql.py 的补充，提供额外的会员基础数据分析功能
"""
import logging

# 配置日志
logger = logging.getLogger(__name__)

class MemberBaseCalculatorAdd:
    """会员基础数据扩展计算器"""

    @staticmethod
    def calculate_phone_member_ratio(total_card_phone_num: int, total_all_user: int) -> float:
        """计算完善手机号会员占比

        计算公式：完善手机号会员占比 = 完善手机号会员数量 / 会员总数量 × 100

        Args:
            total_card_phone_num: 完善手机号会员数量
            total_all_user: 会员总数量

        Returns:
            完善手机号会员占比（百分比），保留两位小数
        """
        try:
            # 确保数据类型转换
            total_card_phone_num = int(total_card_phone_num) if total_card_phone_num is not None else 0
            total_all_user = int(total_all_user) if total_all_user is not None else 0

            if total_all_user == 0:
                logger.warning("会员总数量为0，完善手机号会员占比无法计算")
                return 0.0

            # 计算完善手机号会员占比（百分比）
            phone_ratio = (total_card_phone_num / total_all_user) * 100
            result = round(phone_ratio, 2)

            logger.debug(f"完善手机号会员占比计算: {total_card_phone_num} / {total_all_user} * 100 = {result}%")
            return result

        except Exception as e:
            logger.error(f"计算完善手机号会员占比失败: {str(e)}")
            return 0.0

    @staticmethod
    def calculate_prepay_member_ratio(total_all_user_charger: int, total_all_user: int) -> float:
        """计算储值会员占比

        计算公式：储值会员占比 = 累计储值的会员数 / 会员总数量 × 100

        Args:
            total_all_user_charger: 累计储值的会员数
            total_all_user: 会员总数量

        Returns:
            储值会员占比（百分比），保留两位小数
        """
        try:
            # 确保数据类型转换
            total_all_user_charger = int(total_all_user_charger) if total_all_user_charger is not None else 0
            total_all_user = int(total_all_user) if total_all_user is not None else 0

            if total_all_user == 0:
                logger.warning("会员总数量为0，储值会员占比无法计算")
                return 0.0

            # 计算储值会员占比（百分比）
            prepay_ratio = (total_all_user_charger / total_all_user) * 100
            result = round(prepay_ratio, 2)

            logger.debug(f"储值会员占比计算: {total_all_user_charger} / {total_all_user} * 100 = {result}%")
            return result

        except Exception as e:
            logger.error(f"计算储值会员占比失败: {str(e)}")
            return 0.0

    @staticmethod
    def calculate_consume_member_ratio(total_all_user_consomer: int, total_all_user: int) -> float:
        """计算会员消费人数占比

        计算公式：会员消费人数占比 = 累计消费的会员数 / 会员总数量 × 100

        Args:
            total_all_user_consomer: 累计消费的会员数
            total_all_user: 会员总数量

        Returns:
            会员消费人数占比（百分比），保留两位小数
        """
        try:
            # 确保数据类型转换
            total_all_user_consomer = int(total_all_user_consomer) if total_all_user_consomer is not None else 0
            total_all_user = int(total_all_user) if total_all_user is not None else 0

            if total_all_user == 0:
                logger.warning("会员总数量为0，会员消费人数占比无法计算")
                return 0.0

            # 计算会员消费人数占比（百分比）
            consume_ratio = (total_all_user_consomer / total_all_user) * 100
            result = round(consume_ratio, 2)

            logger.debug(f"会员消费人数占比计算: {total_all_user_consomer} / {total_all_user} * 100 = {result}%")
            return result

        except Exception as e:
            logger.error(f"计算会员消费人数占比失败: {str(e)}")
            return 0.0

    @staticmethod
    def merge_member_ratio_data(base_data: dict) -> dict:
        """合并会员占比数据

        Args:
            base_data: 基础会员数据

        Returns:
            合并后的数据字典
        """
        try:
            logger.debug("开始合并会员占比数据")

            # 合并基础数据
            result = base_data.copy()

            # 获取基础数据
            total_all_user = result.get('total_all_user', 0) or 0
            total_card_phone_num = result.get('total_card_phone_num', 0) or 0
            total_all_user_charger = result.get('total_all_user_charger', 0) or 0
            total_all_user_consomer = result.get('total_all_user_consomer', 0) or 0

            # 计算完善手机号会员占比
            phone_member_ratio = MemberBaseCalculatorAdd.calculate_phone_member_ratio(
                total_card_phone_num, total_all_user
            )
            result['phone_member_ratio'] = phone_member_ratio

            # 计算储值会员占比
            prepay_member_ratio = MemberBaseCalculatorAdd.calculate_prepay_member_ratio(
                total_all_user_charger, total_all_user
            )
            result['prepay_member_ratio'] = prepay_member_ratio

            # 计算会员消费人数占比
            consume_member_ratio = MemberBaseCalculatorAdd.calculate_consume_member_ratio(
                total_all_user_consomer, total_all_user
            )
            result['consume_member_ratio'] = consume_member_ratio

            logger.debug(f"会员占比数据合并完成，新增字段: phone_member_ratio, prepay_member_ratio, consume_member_ratio")
            return result

        except Exception as e:
            logger.error(f"合并会员占比数据失败: {str(e)}")
            return base_data