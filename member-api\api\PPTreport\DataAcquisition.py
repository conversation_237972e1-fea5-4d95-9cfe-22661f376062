# -*- coding: utf-8 -*-
"""
PPT报告数据获取服务
从query模块获取数据并转换为PPT所需格式
"""

import logging
from typing import Dict, Any, Optional
import asyncio

from core.models import QueryParams
from api.query.MemberBaseTab import member_base_service
from api.query.MemberConsumeTab import member_consume_service
from api.query.MemberChargeTab import member_charge_service
from api.query.CouponTradeTab import CouponTradeTab
from .PinzhiData import pinzhi_data_service
from .IndustryData import industry_data_service

logger = logging.getLogger(__name__)

class DataAcquisitionService:
    """数据获取服务类"""

    def __init__(self):
        """初始化数据获取服务"""
        self.member_base_service = member_base_service
        self.member_consume_service = member_consume_service
        self.member_charge_service = member_charge_service
        self.coupon_trade_service = None
        self.pinzhi_data_service = pinzhi_data_service
        self.industry_data_service = industry_data_service

        logger.info("数据获取服务初始化完成")

    async def get_all_ppt_data(self, query_params: QueryParams) -> Dict[str, Any]:
        """
        获取PPT生成所需的所有数据

        Args:
            query_params: 查询参数

        Returns:
            Dict: 包含所有数据的字典
        """
        try:
            logger.info(f"开始获取PPT数据 - 查询参数: {query_params}")

            # 并行获取各模块数据
            tasks = []

            # 会员基础数据
            tasks.append(self._get_member_base_data_safe(query_params))
            # 会员消费数据
            tasks.append(self._get_member_consume_data_safe(query_params))
            # 会员充值数据
            tasks.append(self._get_member_charge_data_safe(query_params))
            # 优惠券数据
            tasks.append(self._get_coupon_data_safe(query_params))
            # 品智收银数据
            tasks.append(self._get_pinzhi_data_safe(query_params))

            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            member_base_data = results[0] if not isinstance(results[0], Exception) else None
            member_consume_data = results[1] if not isinstance(results[1], Exception) else None
            member_charge_data = results[2] if not isinstance(results[2], Exception) else None
            coupon_data = results[3] if not isinstance(results[3], Exception) else None
            pinzhi_data = results[4] if not isinstance(results[4], Exception) else None

            # 记录错误
            module_names = ["会员基础数据", "会员消费数据", "会员充值数据", "优惠券数据", "品智收银数据"]
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"获取{module_names[i]}失败: {str(result)}")

            # 提取基础数据
            extracted_member_base = self._extract_member_base_data(member_base_data)
            extracted_member_consume = self._extract_member_consume_data(member_consume_data)
            extracted_coupon = self._extract_coupon_data(coupon_data)

            # 生成行业分析数据（第19页PPT）- 直接从query模块获取
            logger.info("开始获取行业分析数据...")
            industry_data = await self.industry_data_service.get_industry_analysis_data(query_params)
            logger.info(f"行业分析数据获取完成，数据量: {len(industry_data) if industry_data else 0}")
            if industry_data:
                logger.debug(f"行业分析数据样例: {list(industry_data.keys())[:5]}")  # 只显示前5个键

            # 构建返回数据
            ppt_data = {
                "member_base": extracted_member_base,
                "member_consume": extracted_member_consume,
                "member_charge": self._extract_member_charge_data(member_charge_data),
                "coupon": extracted_coupon,
                "pinzhi": self._extract_pinzhi_data(pinzhi_data),
                "industry": industry_data,  # 添加行业分析数据
                "query_params": {
                    "query_type": query_params.query_type,
                    "bid": query_params.bid,
                    "sid": query_params.sid,
                    "start_date": query_params.start_date,
                    "end_date": query_params.end_date,
                    "cashier_system": query_params.cashier_system,
                    "merchant_id": query_params.merchant_id,
                    "time_frame": self._format_time_frame(query_params)
                }
            }

            logger.info("PPT数据获取完成")
            return ppt_data

        except Exception as e:
            logger.error(f"获取PPT数据失败: {str(e)}", exc_info=True)
            raise

    async def _get_member_base_data_safe(self, query_params: QueryParams) -> Optional[Any]:
        """安全获取会员基础数据"""
        try:
            return await self.member_base_service.get_member_base_data(query_params)
        except Exception as e:
            logger.error(f"获取会员基础数据异常: {str(e)}")
            return None

    async def _get_member_consume_data_safe(self, query_params: QueryParams) -> Optional[Any]:
        """安全获取会员消费数据"""
        try:
            return await self.member_consume_service.get_member_consume_data(query_params)
        except Exception as e:
            logger.error(f"获取会员消费数据异常: {str(e)}")
            return None

    async def _get_member_charge_data_safe(self, query_params: QueryParams) -> Optional[Any]:
        """安全获取会员充值数据"""
        try:
            return await self.member_charge_service.get_member_charge_data(query_params)
        except Exception as e:
            logger.error(f"获取会员充值数据异常: {str(e)}")
            return None

    async def _get_coupon_data_safe(self, query_params: QueryParams) -> Optional[Any]:
        """安全获取优惠券数据"""
        try:
            if self.coupon_trade_service is None:
                self.coupon_trade_service = CouponTradeTab()

            result = await self.coupon_trade_service.get_coupon_trade_data(query_params)

            # 处理ResponseModel对象
            if result and hasattr(result, 'data'):
                return result.data
            elif result and hasattr(result, 'model_dump'):
                result_dict = result.model_dump()
                return result_dict.get("data")
            elif isinstance(result, dict):
                return result.get("data")
            else:
                logger.warning(f"优惠券数据格式异常: {type(result)}")
                return None

        except Exception as e:
            logger.error(f"获取优惠券数据异常: {str(e)}")
            return None

    async def _get_pinzhi_data_safe(self, query_params: QueryParams) -> Optional[Any]:
        """安全获取品智收银数据"""
        try:
            return await self.pinzhi_data_service.get_pinzhi_data_for_ppt(query_params)
        except Exception as e:
            logger.error(f"获取品智收银数据异常: {str(e)}")
            return None

    def _extract_member_base_data(self, raw_data: Any) -> Dict[str, Any]:
        """提取会员基础数据 - 从FieldDataModel中提取value值"""
        if not raw_data:
            return {}

        try:
            # 如果是Pydantic模型，转换为字典
            if hasattr(raw_data, 'model_dump'):
                data_dict = raw_data.model_dump()
            elif isinstance(raw_data, dict):
                data_dict = raw_data
            else:
                logger.warning(f"未知的会员基础数据格式: {type(raw_data)}")
                return {}

            # 提取每个字段的value值，构建扁平化的数据字典
            extracted_data = {}
            for field_name, field_data in data_dict.items():
                if isinstance(field_data, dict) and 'value' in field_data:
                    # 提取FieldDataModel中的value
                    extracted_data[field_name] = field_data['value']
                else:
                    # 如果不是FieldDataModel格式，直接使用原值
                    extracted_data[field_name] = field_data

            logger.info(f"提取的会员基础数据字段数: {len(extracted_data)}")
            logger.debug(f"会员基础数据详情: {extracted_data}")
            return extracted_data

        except Exception as e:
            logger.error(f"提取会员基础数据失败: {str(e)}")
            return {}

    def _extract_member_consume_data(self, raw_data: Any) -> Dict[str, Any]:
        """提取会员消费数据 - 从FieldDataModel中提取value值"""
        if not raw_data:
            return {}

        try:
            # 如果是Pydantic模型，转换为字典
            if hasattr(raw_data, 'model_dump'):
                data_dict = raw_data.model_dump()
            elif isinstance(raw_data, dict):
                data_dict = raw_data
            else:
                logger.warning(f"未知的会员消费数据格式: {type(raw_data)}")
                return {}

            # 提取每个字段的value值，构建扁平化的数据字典
            extracted_data = {}
            for field_name, field_data in data_dict.items():
                if isinstance(field_data, dict) and 'value' in field_data:
                    # 提取FieldDataModel中的value
                    extracted_data[field_name] = field_data['value']
                else:
                    # 如果不是FieldDataModel格式，直接使用原值
                    extracted_data[field_name] = field_data

            logger.info(f"提取的会员消费数据字段数: {len(extracted_data)}")
            logger.debug(f"会员消费数据详情: {extracted_data}")
            return extracted_data

        except Exception as e:
            logger.error(f"提取会员消费数据失败: {str(e)}")
            return {}

    def _extract_member_charge_data(self, raw_data: Any) -> Dict[str, Any]:
        """提取会员充值数据 - 从FieldDataModel中提取value值"""
        if not raw_data:
            return {}

        try:
            # 如果是Pydantic模型，转换为字典
            if hasattr(raw_data, 'model_dump'):
                data_dict = raw_data.model_dump()
            elif isinstance(raw_data, dict):
                data_dict = raw_data
            else:
                logger.warning(f"未知的会员充值数据格式: {type(raw_data)}")
                return {}

            # 提取每个字段的value值，构建扁平化的数据字典
            extracted_data = {}
            for field_name, field_data in data_dict.items():
                if isinstance(field_data, dict) and 'value' in field_data:
                    # 提取FieldDataModel中的value
                    extracted_data[field_name] = field_data['value']
                else:
                    # 如果不是FieldDataModel格式，直接使用原值
                    extracted_data[field_name] = field_data

            logger.info(f"提取的会员充值数据字段数: {len(extracted_data)}")
            logger.debug(f"会员充值数据详情: {extracted_data}")
            return extracted_data

        except Exception as e:
            logger.error(f"提取会员充值数据失败: {str(e)}")
            return {}

    def _extract_coupon_data(self, raw_data: Any) -> Dict[str, Any]:
        """提取优惠券数据"""
        if not raw_data:
            return {}

        try:
            # 处理不同格式的优惠券数据
            if isinstance(raw_data, list):
                # 如果直接是列表，包装成字典格式
                coupon_list = raw_data
            elif isinstance(raw_data, dict):
                # 如果是字典，检查是否有coupon_list字段
                if "coupon_list" in raw_data:
                    coupon_list = raw_data["coupon_list"]
                else:
                    # 假设字典本身就是优惠券数据，转换为列表
                    coupon_list = [raw_data] if raw_data else []
            elif hasattr(raw_data, 'model_dump'):
                data_dict = raw_data.model_dump()
                coupon_list = data_dict if isinstance(data_dict, list) else [data_dict]
            else:
                logger.warning(f"未知的优惠券数据格式: {type(raw_data)}")
                return {}

            # 按带动现金消费金额降序排序，选择前8个优惠券
            if coupon_list:
                # 排序：按 driveCashAmount 降序排序
                coupon_list = sorted(
                    coupon_list,
                    key=lambda x: float(x.get('driveCashAmount', 0)) if isinstance(x, dict) else 0,
                    reverse=True
                )
                # 只保留前8个优惠券
                coupon_list = coupon_list[:8]
                logger.info(f"优惠券按带动现金消费金额排序后，选择前{len(coupon_list)}个优惠券")

            # 计算汇总数据
            total_send_count = 0
            total_used_count = 0
            total_drive_amount = 0.0

            for coupon in coupon_list:
                if isinstance(coupon, dict):
                    # 适配CouponTradeTab.py的数据格式
                    send_count = int(coupon.get('couponSendCount', coupon.get('send_count', 0)))
                    used_count = int(coupon.get('couponUsedCount', coupon.get('used_count', 0)))
                    drive_amount = float(coupon.get('driveTotalAmount', coupon.get('drive_amount', 0)))

                    total_send_count += send_count
                    total_used_count += used_count
                    total_drive_amount += drive_amount

            # 计算平均使用率
            avg_usage_rate = (total_used_count / total_send_count * 100) if total_send_count > 0 else 0

            # 构建标准格式的优惠券数据
            result = {
                "coupon_list": coupon_list,
                "total_send_count": total_send_count,
                "total_used_count": total_used_count,
                "total_drive_amount": total_drive_amount,
                "avg_usage_rate": round(avg_usage_rate, 1)
            }

            logger.info(f"提取的优惠券数据汇总: 发券{total_send_count}张, 使用{total_used_count}张, 使用率{avg_usage_rate:.1f}%")
            return result

        except Exception as e:
            logger.error(f"提取优惠券数据失败: {str(e)}")
            return {}

    def _extract_pinzhi_data(self, raw_data: Any) -> Dict[str, Any]:
        """提取品智收银数据"""
        if not raw_data:
            return {}

        try:
            # 品智数据已经在PinzhiData.py中处理为标准格式
            if isinstance(raw_data, dict):
                return raw_data
            else:
                logger.warning(f"未知的品智数据格式: {type(raw_data)}")
                return {}

        except Exception as e:
            logger.error(f"提取品智数据失败: {str(e)}")
            return {}

    def _format_time_frame(self, query_params: QueryParams) -> str:
        """格式化时间范围为中文描述"""
        try:
            if query_params.start_date and query_params.end_date:
                start_date = query_params.start_date
                end_date = query_params.end_date

                # 转换为中文格式
                start_parts = start_date.split('-')
                end_parts = end_date.split('-')

                if len(start_parts) == 3 and len(end_parts) == 3:
                    start_year, start_month, start_day = start_parts
                    end_year, end_month, end_day = end_parts

                    # 如果是同一年
                    if start_year == end_year:
                        # 如果是同一月
                        if start_month == end_month:
                            return f"{start_year}年{int(start_month)}月{int(start_day)}日-{int(end_day)}日"
                        else:
                            return f"{start_year}年{int(start_month)}月{int(start_day)}日-{int(end_month)}月{int(end_day)}日"
                    else:
                        return f"{start_year}年{int(start_month)}月{int(start_day)}日-{end_year}年{int(end_month)}月{int(end_day)}日"

            # 根据查询类型生成默认时间范围
            query_type_map = {
                "week": "本周",
                "month": "本月",
                "quarter": "本季度",
                "halfyear": "上半年",
                "custom": "自定义时间范围"
            }

            return query_type_map.get(query_params.query_type, "2024年1月-12月")

        except Exception as e:
            logger.error(f"格式化时间范围失败: {str(e)}")
            return "2024年1月-12月"

    async def get_member_base_data_only(self, query_params: QueryParams) -> Dict[str, Any]:
        """仅获取会员基础数据"""
        try:
            raw_data = await self._get_member_base_data_safe(query_params)
            return self._extract_member_base_data(raw_data)
        except Exception as e:
            logger.error(f"获取会员基础数据失败: {str(e)}")
            return {}

    async def get_member_consume_data_only(self, query_params: QueryParams) -> Dict[str, Any]:
        """仅获取会员消费数据"""
        try:
            raw_data = await self._get_member_consume_data_safe(query_params)
            return self._extract_member_consume_data(raw_data)
        except Exception as e:
            logger.error(f"获取会员消费数据失败: {str(e)}")
            return {}

    async def get_member_charge_data_only(self, query_params: QueryParams) -> Dict[str, Any]:
        """仅获取会员充值数据"""
        try:
            raw_data = await self._get_member_charge_data_safe(query_params)
            return self._extract_member_charge_data(raw_data)
        except Exception as e:
            logger.error(f"获取会员充值数据失败: {str(e)}")
            return {}

    async def get_coupon_data_only(self, query_params: QueryParams) -> Dict[str, Any]:
        """仅获取优惠券数据"""
        try:
            raw_data = await self._get_coupon_data_safe(query_params)
            return self._extract_coupon_data(raw_data)
        except Exception as e:
            logger.error(f"获取优惠券数据失败: {str(e)}")
            return {}

    async def get_pinzhi_data_only(self, query_params: QueryParams) -> Dict[str, Any]:
        """仅获取品智收银数据"""
        try:
            raw_data = await self._get_pinzhi_data_safe(query_params)
            return self._extract_pinzhi_data(raw_data)
        except Exception as e:
            logger.error(f"获取品智收银数据失败: {str(e)}")
            return {}


# 创建全局服务实例
data_acquisition_service = DataAcquisitionService()