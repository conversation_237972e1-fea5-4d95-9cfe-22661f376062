from functools import wraps
import json
from typing import Any
from fastapi import Request
from pydantic import BaseModel
from logger import logger

def log_api_call(endpoint_name: str):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                # 获取请求对象和请求数据
                request = next((arg for arg in args if isinstance(arg, Request)), None)
                request_data = next((
                    value.dict() 
                    for value in kwargs.values() 
                    if isinstance(value, BaseModel)
                ), {})
                
                # 如果是登录请求，隐藏密码
                if 'password' in request_data:
                    request_data['password'] = '****'
                
                # 记录请求信息
                logger.info(f"=== {endpoint_name} API Call ===")
                if request:
                    logger.info(f"Method: {request.method}")
                    logger.info(f"Path: {request.url.path}")
                logger.info(f"Request Data: {json.dumps(request_data, ensure_ascii=False)}")
                
                # 执行API处理
                response = await func(*args, **kwargs)
                
                # 记录响应信息
                logger.info(f"Response: {json.dumps(response, ensure_ascii=False)}")
                logger.info(f"=== End {endpoint_name} API Call ===")
                
                return response
                
            except Exception as e:
                logger.error(f"Error in {endpoint_name} API: {str(e)}")
                raise
            
        return wrapper
    return decorator 