# -*- coding: utf-8 -*-
"""
PPT生成服务
提供统一的PPT生成接口，封装PPT生成逻辑
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import datetime

# 设置logger
logger = logging.getLogger(__name__)

# 使用绝对路径导入SimplePptGenerator
import importlib.util

# 获取项目根目录和SimplePptGenerator文件路径
project_root = Path(__file__).parent.parent
simple_ppt_generator_path = project_root / "function" / "ai-pptx" / "data_reports" / "simple_ppt_generator.py"

# 动态导入SimplePptGenerator类
try:
    spec = importlib.util.spec_from_file_location("simple_ppt_generator", simple_ppt_generator_path)
    if spec is None:
        raise ImportError(f"无法找到SimplePptGenerator文件: {simple_ppt_generator_path}")

    simple_ppt_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(simple_ppt_module)
    SimplePptGenerator = simple_ppt_module.SimplePptGenerator

    logger.info(f"成功导入SimplePptGenerator类: {SimplePptGenerator}")

except Exception as e:
    logger.error(f"导入SimplePptGenerator失败: {str(e)}")
    raise ImportError(f"无法导入SimplePptGenerator: {str(e)}")

from core.config import settings
from .oss_service import oss_service

class PPTService:
    """PPT生成服务类"""

    def __init__(self):
        """初始化PPT服务"""
        self.project_root = Path(__file__).parent.parent
        self.template_path = self.project_root / "ppt_template" / "会员数据报告-模板.pptx"
        # 修改输出目录为uploads，避免在ppt_template中生成文件
        self.output_dir = self.project_root / "uploads" / "temp"

        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 初始化OSS服务
        file_config = settings.get_file_service_config()
        self.oss_service = oss_service
        self.oss_service.config.update(file_config)
        # 确保base_url正确更新
        self.oss_service.base_url = file_config.get("base_url", "http://localhost:8000")
        logger.info(f"OSS服务配置更新 - base_url: {self.oss_service.base_url}")

        logger.info(f"PPT服务初始化完成")
        logger.info(f"  - 模板路径: {self.template_path}")
        logger.info(f"  - 临时输出目录: {self.output_dir}")
        logger.info(f"  - 最终存储目录: {self.oss_service.upload_path}/ppt-reports")

        # 清理可能存在的旧的ppt_template目录中的生成文件
        self._cleanup_old_generated_files()

    def _cleanup_old_generated_files(self):
        """清理ppt_template目录中可能存在的旧生成文件"""
        try:
            ppt_template_dir = self.project_root / "ppt_template"
            if ppt_template_dir.exists():
                # 查找所有非模板的pptx文件
                for file_path in ppt_template_dir.glob("*.pptx"):
                    if file_path.name != "会员数据报告-模板.pptx":
                        logger.info(f"清理旧生成文件: {file_path}")
                        file_path.unlink()

                logger.info("ppt_template目录清理完成")
        except Exception as e:
            logger.warning(f"清理旧文件时出错: {e}")

    def generate_ppt_report(self, data_dict: Dict[str, Any], output_filename: Optional[str] = None) -> Dict[str, Any]:
        """
        生成PPT报告

        Args:
            data_dict: 包含所有PPT参数的数据字典
            output_filename: 输出文件名（可选，默认使用时间戳）

        Returns:
            Dict: 生成结果，包含成功状态、文件路径等信息
        """
        try:
            logger.info("开始生成PPT报告...")

            # 1. 验证模板文件是否存在
            if not self.template_path.exists():
                error_msg = f"PPT模板文件不存在: {self.template_path}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "file_path": None
                }

            # 2. 生成输出文件路径
            if not output_filename:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                output_filename = f"会员数据报告_{timestamp}.pptx"

            output_path = self.output_dir / output_filename

            # 3. 创建PPT生成器
            generator = SimplePptGenerator(str(self.template_path), str(output_path))

            # 4. 验证参数匹配情况
            logger.info("验证PPT模板参数...")
            missing_params, extra_params = generator.validate_template_params(data_dict)

            if missing_params:
                logger.warning(f"缺少的参数: {sorted(missing_params)}")

            if extra_params:
                logger.info(f"额外的参数: {sorted(extra_params)}")

            # 5. 生成PPT报告
            logger.info("开始生成PPT文件...")
            success = generator.generate_report(data_dict)

            if success:
                file_size = output_path.stat().st_size if output_path.exists() else 0
                logger.info(f"PPT报告生成成功 - 文件: {output_path}, 大小: {file_size / 1024:.1f} KB")

                # 上传文件到OSS
                upload_result = self.oss_service.upload_file(output_path, output_filename)

                if upload_result["success"]:
                    logger.info(f"PPT文件上传成功: {upload_result['file_url']}")

                    # 上传成功后删除临时文件，避免重复存储
                    try:
                        if output_path.exists():
                            output_path.unlink()
                            logger.info(f"临时文件已删除: {output_path}")
                    except Exception as e:
                        logger.warning(f"删除临时文件失败: {e}")

                    return {
                        "success": True,
                        "message": "PPT报告生成成功",
                        "file_path": str(upload_result.get("file_path", output_path)),  # 使用上传后的路径
                        "file_name": output_filename,
                        "file_size": file_size,
                        "file_url": upload_result["file_url"],
                        "download_url": upload_result["file_url"],
                        "object_name": upload_result["object_name"],
                        "upload_time": upload_result["upload_time"],
                        "missing_params": list(missing_params),
                        "extra_params": list(extra_params)
                    }
                else:
                    logger.warning(f"PPT文件上传失败: {upload_result.get('error', '未知错误')}")
                    # 即使上传失败，也返回本地文件信息
                    return {
                        "success": True,
                        "message": "PPT报告生成成功，但文件上传失败",
                        "file_path": str(output_path),
                        "file_name": output_filename,
                        "file_size": file_size,
                        "file_url": None,
                        "download_url": None,
                        "upload_error": upload_result.get("error"),
                        "missing_params": list(missing_params),
                        "extra_params": list(extra_params)
                    }
            else:
                error_msg = "PPT报告生成失败"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "file_path": None
                }

        except Exception as e:
            error_msg = f"生成PPT报告时发生异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "file_path": None
            }

    def validate_template_params(self, data_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证模板参数

        Args:
            data_dict: 数据字典

        Returns:
            Dict: 验证结果
        """
        try:
            if not self.template_path.exists():
                return {
                    "valid": False,
                    "error": f"模板文件不存在: {self.template_path}"
                }

            generator = SimplePptGenerator(str(self.template_path), "temp.pptx")
            missing_params, extra_params = generator.validate_template_params(data_dict)

            return {
                "valid": len(missing_params) == 0,
                "missing_params": list(missing_params),
                "extra_params": list(extra_params),
                "template_params": list(generator.extract_template_params()),
                "data_params": list(data_dict.keys())
            }

        except Exception as e:
            logger.error(f"验证模板参数时发生异常: {str(e)}")
            return {
                "valid": False,
                "error": str(e)
            }

    def get_template_info(self) -> Dict[str, Any]:
        """
        获取模板信息

        Returns:
            Dict: 模板信息
        """
        try:
            if not self.template_path.exists():
                return {
                    "exists": False,
                    "path": str(self.template_path),
                    "error": "模板文件不存在"
                }

            file_size = self.template_path.stat().st_size
            file_mtime = datetime.datetime.fromtimestamp(self.template_path.stat().st_mtime)

            # 提取模板参数
            generator = SimplePptGenerator(str(self.template_path), "temp.pptx")
            template_params = generator.extract_template_params()

            return {
                "exists": True,
                "path": str(self.template_path),
                "file_size": file_size,
                "modified_time": file_mtime.strftime("%Y-%m-%d %H:%M:%S"),
                "template_params": list(template_params),
                "param_count": len(template_params)
            }

        except Exception as e:
            logger.error(f"获取模板信息时发生异常: {str(e)}")
            return {
                "exists": False,
                "path": str(self.template_path),
                "error": str(e)
            }


# 创建全局服务实例
ppt_service = PPTService()