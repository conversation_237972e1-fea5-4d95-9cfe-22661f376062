# -*- coding: utf-8 -*-
"""
会员消费数量分析AI模块
专门处理会员消费数量数据的AI分析功能
"""

import logging
from typing import Dict, Any, List, Optional
import asyncio

from services.llm_service import LLMService
from .ConsumptionNumPromt import ConsumptionNumAnalysisPrompts

logger = logging.getLogger(__name__)

class ConsumptionNumAiAnalyzer:
    """会员消费数量AI分析器"""

    def __init__(self):
        """初始化会员消费数量AI分析器"""
        self.llm_service = LLMService()
        logger.info("会员消费数量AI分析器初始化完成")

    async def analyze_consumption_num_last_year_data(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        分析去年会员消费数量数据

        Args:
            monthly_data: 去年月度消费数量数据列表

        Returns:
            str: AI分析结果
        """
        try:
            if not monthly_data:
                return "去年会员消费数量数据分析：数据不足，无法进行有效分析。"

            # 生成分析提示词
            analysis_prompt = ConsumptionNumAnalysisPrompts.get_consumption_num_last_year_analysis_prompt(monthly_data)

            # 构建完整的AI分析请求
            full_prompt = f"""
作为专业的数据分析师，请基于以下会员消费数量数据进行深入分析：

数据概况：
{self._format_consumption_num_data_summary(monthly_data)}

分析要求：
请按照以下格式生成具有洞见性和实操性的分析报告：
1. 使用"1-4"作为项目符号，按点列出3-4个关键洞察
2. 每个要点包含：消费数量数据洞察 + 具体可执行的实操建议
3. 重点关注：消费规模、消费频次、充值活跃度、季节性波动
4. 避免简单的数据描述，要提供深度分析和根因识别
5. 确保建议具体可执行，避免空泛的建议
6. 每个建议之间不要添加空行，也禁止添加加粗等格式
7. 如果分析不同月份直接的差异，要指出几月对比几月，明确对比的对象，更多的将注意力关注到近期的变化
8. 再分析内容中，每个出现的数据前都要标注清楚是几月份或是几几年，清晰的展示数据来源

基础分析框架：
{analysis_prompt}

请生成按点格式的专业分析报告（每点35-45字）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                logger.info("去年会员消费数量数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果，使用默认分析")
                return analysis_prompt

        except Exception as e:
            logger.error(f"去年会员消费数量数据AI分析失败: {str(e)}")
            # 返回基础分析作为备用
            return ConsumptionNumAnalysisPrompts.get_consumption_num_last_year_analysis_prompt(monthly_data)

    async def analyze_consumption_num_this_year_data(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        分析今年会员消费数量数据（包含对比分析）

        Args:
            this_year_data: 今年月度消费数量数据列表
            last_year_data: 去年同期数据列表（用于对比）

        Returns:
            str: AI分析结果
        """
        try:
            if not this_year_data:
                return "今年会员消费数量数据分析：数据不足，无法进行有效分析。"

            # 生成分析提示词
            analysis_prompt = ConsumptionNumAnalysisPrompts.get_consumption_num_this_year_analysis_prompt(
                this_year_data, last_year_data or []
            )

            # 构建完整的AI分析请求
            comparison_section = ""
            if last_year_data:
                comparison_section = f"""
去年同期数据对比：
{self._format_consumption_num_data_summary(last_year_data[:len(this_year_data)])}
"""

            full_prompt = f"""
作为专业的数据分析师，请基于以下会员消费数量数据进行深入的对比分析，主要关注近期的变化给出改进建议：

今年数据概况：
{self._format_consumption_num_data_summary(this_year_data)}
{comparison_section}

分析要求：
请按照以下格式生成具有洞见性和实操性的对比分析报告：
1. 使用"1-4"作为项目符号，按点列出3-4个关键对比洞察
2. 每个要点包含：同比变化洞察 + 具体可执行的实操建议
3. 重点关注：增长对比、消费效率对比、充值表现对比、发展趋势
4. 深度分析变化背后的原因，提供根因识别
5. 确保建议具体可执行，包含明确的行动方向
6. 每个建议之间不要添加空行，也禁止添加加粗等格式
7. 如果分析不同月份直接的差异，要指出几月对比几月，明确对比的对象，更多的将注意力关注到近期的变化
8. 再分析内容中，每个出现的数据前都要标注清楚是几月份或是几几年，清晰的展示数据来源

基础分析框架：
{analysis_prompt}

请生成按点格式的专业对比分析报告（每点35-45字）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                logger.info("今年会员消费数量数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果，使用默认分析")
                return analysis_prompt

        except Exception as e:
            logger.error(f"今年会员消费数量数据AI分析失败: {str(e)}")
            # 返回基础分析作为备用
            return ConsumptionNumAnalysisPrompts.get_consumption_num_this_year_analysis_prompt(
                this_year_data, last_year_data or []
            )

    def _format_consumption_num_data_summary(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        格式化月度消费数量数据摘要

        Args:
            monthly_data: 月度消费数量数据列表

        Returns:
            str: 格式化的数据摘要
        """
        if not monthly_data:
            return "无数据"

        summary_lines = []
        total_consume_users_sum = 0
        total_consume_count_sum = 0
        total_charge_count_sum = 0

        for item in monthly_data:
            month = item.get('month', '未知月份')
            consume_users = item.get('consume_users', 0)
            total_consume_count = item.get('total_consume_count', 0)
            charge_count = item.get('charge_count', 0)
            consume_frequency = item.get('consume_frequency', 0)

            total_consume_users_sum += consume_users
            total_consume_count_sum += total_consume_count
            total_charge_count_sum += charge_count

            summary_lines.append(
                f"{month}: 消费人数{consume_users}人, 消费笔数{total_consume_count}笔, "
                f"充值笔数{charge_count}笔, 消费频次{consume_frequency:.2f}次/人"
            )

        # 添加汇总信息
        avg_consume_users = total_consume_users_sum / len(monthly_data) if monthly_data else 0
        avg_total_consume = total_consume_count_sum / len(monthly_data) if monthly_data else 0
        avg_charge_count = total_charge_count_sum / len(monthly_data) if monthly_data else 0
        avg_frequency = (total_consume_count_sum / total_consume_users_sum) if total_consume_users_sum > 0 else 0

        summary_lines.append(f"\n汇总: 月均消费人数{avg_consume_users:.0f}人, 月均消费笔数{avg_total_consume:.0f}笔, 月均充值笔数{avg_charge_count:.0f}笔, 平均消费频次{avg_frequency:.2f}次/人")

        return "\n".join(summary_lines)

    async def generate_all_consumption_num_analysis(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        生成所有会员消费数量相关的AI分析

        Args:
            this_year_data: 今年月度消费数量数据列表
            last_year_data: 去年月度消费数量数据列表

        Returns:
            Dict: 包含所有分析结果的字典
        """
        try:
            logger.info("开始生成会员消费数量AI分析...")

            # 并行执行两个分析任务
            tasks = [
                self.analyze_consumption_num_last_year_data(last_year_data),
                self.analyze_consumption_num_this_year_data(this_year_data, last_year_data)
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            last_year_analysis = results[0] if not isinstance(results[0], Exception) else "去年消费数量数据分析失败"
            this_year_analysis = results[1] if not isinstance(results[1], Exception) else "今年消费数量数据分析失败"

            analysis_results = {
                "consumption_num_last_year_analysis_report": last_year_analysis,
                "consumption_num_this_year_analysis_report": this_year_analysis
            }

            logger.info("会员消费数量AI分析生成完成")
            return analysis_results

        except Exception as e:
            logger.error(f"生成会员消费数量AI分析失败: {str(e)}")
            return {
                "consumption_num_last_year_analysis_report": "去年会员消费数量数据分析生成失败",
                "consumption_num_this_year_analysis_report": "今年会员消费数量数据分析生成失败"
            }


# 创建全局会员消费数量AI分析器实例
consumption_num_ai_analyzer = ConsumptionNumAiAnalyzer()