{"version": 3, "sources": ["../../element-plus/es/components/option-group/style/css.mjs", "../../element-plus/es/components/select/style/css.mjs"], "sourcesContent": ["import '../../base/style/css.mjs';\nimport 'element-plus/theme-chalk/el-option-group.css';\n//# sourceMappingURL=css.mjs.map\n", "import '../../base/style/css.mjs';\nimport '../../tag/style/css.mjs';\nimport '../../option/style/css.mjs';\nimport '../../option-group/style/css.mjs';\nimport '../../scrollbar/style/css.mjs';\nimport '../../popper/style/css.mjs';\nimport 'element-plus/theme-chalk/el-select.css';\n//# sourceMappingURL=css.mjs.map\n"], "mappings": ";;;;;;;AACA,OAAO;;;ACKP,OAAO;", "names": []}