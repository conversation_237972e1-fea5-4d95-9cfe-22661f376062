# -*- coding: utf-8 -*-
"""
行业数据获取服务
为PPT报告第19页提供行业分析数据
"""

import logging
import datetime
import asyncio
from typing import Dict, Any
from core.models import QueryParams
from api.query.MemberBaseTab import member_base_service
from api.query.MemberConsumeTab import member_consume_service
from api.query.MemberChargeTab import member_charge_service
from api.query.CouponTradeTab import CouponTradeTab

logger = logging.getLogger(__name__)

class IndustryDataService:
    """行业数据服务类 - 第19页PPT数据获取"""

    def __init__(self):
        """初始化行业数据服务"""
        self.member_base_service = member_base_service
        self.member_consume_service = member_consume_service
        self.member_charge_service = member_charge_service
        self.coupon_trade_service = CouponTradeTab()
        logger.info("行业数据服务初始化完成")

    async def get_industry_analysis_data(self, query_params: QueryParams) -> Dict[str, Any]:
        """
        获取行业分析数据（第19页PPT）
        包含当前时间段和年度数据

        Args:
            query_params: 查询参数

        Returns:
            Dict: 包含当前时间段和年度数据的行业分析指标
        """
        try:
            logger.info(f"开始获取行业分析数据，参数: {query_params}")

            # 获取当前时间段数据
            current_data = await self._get_current_period_data(query_params)

            # 获取年度数据
            year_data = await self._get_year_data(query_params)

            # 合并数据
            result = {**current_data, **year_data}

            logger.info(f"行业分析数据获取完成，共{len(result)}个字段")
            return result

        except Exception as e:
            logger.error(f"获取行业分析数据失败: {str(e)}", exc_info=True)
            return self._get_empty_industry_data()

    async def _get_current_period_data(self, query_params: QueryParams) -> Dict[str, Any]:
        """获取当前时间段的行业分析数据"""
        try:
            logger.info(f"获取当前时间段数据: {query_params.start_date} - {query_params.end_date}")

            # 并行获取四个模块的数据
            member_base_data, member_consume_data, member_charge_data, coupon_data = await asyncio.gather(
                self.member_base_service.get_member_base_data(query_params),
                self.member_consume_service.get_member_consume_data(query_params),
                self.member_charge_service.get_member_charge_data(query_params),
                self.coupon_trade_service.get_coupon_trade_data(query_params),
                return_exceptions=True
            )

            # 处理异常结果
            if isinstance(member_base_data, Exception):
                logger.error(f"获取会员基础数据失败: {member_base_data}")
                member_base_data = None
            if isinstance(member_consume_data, Exception):
                logger.error(f"获取会员消费数据失败: {member_consume_data}")
                member_consume_data = None
            if isinstance(member_charge_data, Exception):
                logger.error(f"获取会员充值数据失败: {member_charge_data}")
                member_charge_data = None
            if isinstance(coupon_data, Exception):
                logger.error(f"获取券交易数据失败: {coupon_data}")
                coupon_data = None

            # 提取和计算指标
            result = self._extract_industry_indicators(member_base_data, member_consume_data, member_charge_data, coupon_data)

            logger.info(f"当前时间段数据获取完成，共{len(result)}个指标")
            return result

        except Exception as e:
            logger.error(f"获取当前时间段数据失败: {str(e)}", exc_info=True)
            return self._get_empty_current_data()

    async def _get_year_data(self, query_params: QueryParams) -> Dict[str, Any]:
        """获取年度数据（今年1月1日到昨天）"""
        try:
            # 计算年度时间范围
            today = datetime.date.today()
            year_start = datetime.date(today.year, 1, 1)
            year_end = today - datetime.timedelta(days=1)  # 昨天，因为当天数据不全

            # 创建年度查询参数
            year_params = QueryParams(
                query_type=query_params.query_type,
                bid=query_params.bid,
                sid=query_params.sid,
                start_date=year_start.strftime("%Y-%m-%d"),
                end_date=year_end.strftime("%Y-%m-%d"),
                cashier_system=query_params.cashier_system,
                merchant_id=query_params.merchant_id
            )

            logger.info(f"获取年度数据: {year_params.start_date} - {year_params.end_date}")

            # 并行获取四个模块的年度数据
            member_base_data, member_consume_data, member_charge_data, coupon_data = await asyncio.gather(
                self.member_base_service.get_member_base_data(year_params),
                self.member_consume_service.get_member_consume_data(year_params),
                self.member_charge_service.get_member_charge_data(year_params),
                self.coupon_trade_service.get_coupon_trade_data(year_params),
                return_exceptions=True
            )

            # 处理异常结果
            if isinstance(member_base_data, Exception):
                logger.error(f"获取年度会员基础数据失败: {member_base_data}")
                member_base_data = None
            if isinstance(member_consume_data, Exception):
                logger.error(f"获取年度会员消费数据失败: {member_consume_data}")
                member_consume_data = None
            if isinstance(member_charge_data, Exception):
                logger.error(f"获取年度会员充值数据失败: {member_charge_data}")
                member_charge_data = None
            if isinstance(coupon_data, Exception):
                logger.error(f"获取年度券交易数据失败: {coupon_data}")
                coupon_data = None

            # 提取和计算指标，添加_year后缀
            year_indicators = self._extract_industry_indicators(member_base_data, member_consume_data, member_charge_data, coupon_data)
            result = {f"{key}_year": value for key, value in year_indicators.items()}

            logger.info(f"年度数据获取完成，共{len(result)}个指标")
            return result

        except Exception as e:
            logger.error(f"获取年度数据失败: {str(e)}", exc_info=True)
            return self._get_empty_year_data()

    def _extract_industry_indicators(self, member_base_data, member_consume_data, member_charge_data, coupon_data) -> Dict[str, Any]:
        """从原始数据中提取行业分析指标"""
        try:
            result = {}

            # 1. 从会员基础数据提取指标
            if member_base_data:
                result['phone_member_ratio'] = self._extract_field_value(member_base_data, 'phone_member_ratio')
                result['prepay_member_ratio'] = self._extract_field_value(member_base_data, 'prepay_member_ratio')
                result['consume_member_ratio'] = self._extract_field_value(member_base_data, 'consume_member_ratio')
            else:
                result['phone_member_ratio'] = 0.0
                result['prepay_member_ratio'] = 0.0
                result['consume_member_ratio'] = 0.0

            # 2. 从会员消费数据提取和计算指标
            if member_consume_data:
                # 直接提取的字段
                result['repurchase_rate'] = self._extract_field_value(member_consume_data, 'repurchase_rate')
                result['consume_frequency'] = self._extract_field_value(member_consume_data, 'consume_frequency')
                result['prepay_consumption_ratio'] = self._extract_field_value(member_consume_data, 'prepay_consumption_ratio')

                # 需要计算比例的字段
                consume_users = self._extract_field_value(member_consume_data, 'consume_users')
                if consume_users > 0:
                    consume_once_members = self._extract_field_value(member_consume_data, 'consume_once_members')
                    consume_twice_members = self._extract_field_value(member_consume_data, 'consume_twice_members')
                    consume_thrice_members = self._extract_field_value(member_consume_data, 'consume_thrice_members')
                    consume_more_than_thrice_members = self._extract_field_value(member_consume_data, 'consume_more_than_thrice_members')

                    result['consume_once_members_ratio'] = round(consume_once_members / consume_users, 4)
                    result['consume_twice_members_ratio'] = round(consume_twice_members / consume_users, 4)
                    result['consume_thrice_members_ratio'] = round(consume_thrice_members / consume_users, 4)
                    result['consume_more_than_thrice_members_ratio'] = round(consume_more_than_thrice_members / consume_users, 4)
                else:
                    result['consume_once_members_ratio'] = 0.0
                    result['consume_twice_members_ratio'] = 0.0
                    result['consume_thrice_members_ratio'] = 0.0
                    result['consume_more_than_thrice_members_ratio'] = 0.0
            else:
                result['consume_once_members_ratio'] = 0.0
                result['consume_twice_members_ratio'] = 0.0
                result['consume_thrice_members_ratio'] = 0.0
                result['consume_more_than_thrice_members_ratio'] = 0.0
                result['repurchase_rate'] = 0.0
                result['consume_frequency'] = 0.0
                result['prepay_consumption_ratio'] = 0.0

            # 3. 从会员充值数据提取储值留存率
            if member_charge_data:
                result['prepay_retention_rate'] = self._extract_field_value(member_charge_data, 'retention_rate')
            else:
                result['prepay_retention_rate'] = 0.0

            # 4. 从券交易数据提取指标
            if coupon_data:
                # 处理ResponseModel格式的券数据
                if hasattr(coupon_data, 'data') and coupon_data.data:
                    result['coupon_usage_rate'] = self._calculate_coupon_usage_rate(coupon_data.data)
                else:
                    result['coupon_usage_rate'] = 0.0
            else:
                result['coupon_usage_rate'] = 0.0

            return result

        except Exception as e:
            logger.error(f"提取行业分析指标失败: {str(e)}", exc_info=True)
            return self._get_empty_current_data()

    def _extract_field_value(self, data, field: str) -> float:
        """从数据中提取数值（处理Pydantic模型和FieldDataModel）"""
        try:
            if not data:
                return 0.0

            # 如果是Pydantic模型，转换为字典
            if hasattr(data, 'model_dump'):
                data_dict = data.model_dump()
            elif isinstance(data, dict):
                data_dict = data
            else:
                logger.warning(f"未知的数据格式: {type(data)}")
                return 0.0

            if field not in data_dict:
                return 0.0

            field_data = data_dict[field]

            # 处理FieldDataModel格式
            if isinstance(field_data, dict) and 'value' in field_data:
                return float(field_data['value'])
            elif isinstance(field_data, (int, float)):
                return float(field_data)
            else:
                return 0.0

        except (ValueError, TypeError) as e:
            logger.warning(f"提取字段 {field} 数值失败: {str(e)}，使用默认值0")
            return 0.0

    def _calculate_coupon_usage_rate(self, coupon_data_list) -> float:
        """计算券核销率"""
        try:
            if not coupon_data_list:
                return 0.0

            total_sent = 0
            total_used = 0

            # 处理券数据列表
            for coupon in coupon_data_list:
                if isinstance(coupon, dict):
                    sent = coupon.get('couponSendCount', 0) or 0
                    used = coupon.get('couponUsedCount', 0) or 0
                    total_sent += sent
                    total_used += used

            if total_sent == 0:
                return 0.0

            usage_rate = total_used / total_sent
            return round(usage_rate, 4)

        except Exception as e:
            logger.warning(f"计算券核销率失败: {str(e)}")
            return 0.0

    def _get_empty_current_data(self) -> Dict[str, Any]:
        """返回空的当前时间段数据"""
        return {
            'phone_member_ratio': 0.0,
            'prepay_member_ratio': 0.0,
            'consume_member_ratio': 0.0,
            'consume_once_members_ratio': 0.0,
            'consume_twice_members_ratio': 0.0,
            'consume_thrice_members_ratio': 0.0,
            'consume_more_than_thrice_members_ratio': 0.0,
            'repurchase_rate': 0.0,
            'consume_frequency': 0.0,
            'prepay_consumption_ratio': 0.0,
            'prepay_retention_rate': 0.0,
            'coupon_usage_rate': 0.0
        }

    def _get_empty_year_data(self) -> Dict[str, Any]:
        """返回空的年度数据"""
        empty_current = self._get_empty_current_data()
        return {f"{key}_year": value for key, value in empty_current.items()}

    def _get_empty_industry_data(self) -> Dict[str, Any]:
        """返回空的完整行业数据（包含当前和年度）"""
        current_data = self._get_empty_current_data()
        year_data = self._get_empty_year_data()
        return {**current_data, **year_data}


# 创建服务实例
industry_data_service = IndustryDataService()
