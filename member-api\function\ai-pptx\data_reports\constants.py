# -*- coding: utf-8 -*-
"""
会员数据报告常量定义文件
包含所有PPT模板参数的定义、注释和示例数据
按照PPT页数进行组织，方便后续修改

使用方法:
1. 修改下面的示例数据为实际数据
2. 运行 generate_member_report.py 生成报告
"""

import datetime

# ==================== PPT第1页 - 基础配置 ====================

# 报告时间范围，选择前端传递的数据：time_frame
TIME_FRAME = "2024年1月-12月"

# 报告生成日期
REPORT_DATE = datetime.datetime.now().strftime("%Y年%m月%d日")

# ==================== PPT第6页 - 会员数据分析报告 ====================

# 会员数据分析报告标题：member_data_analysis_report
MEMBER_DATA_ANALYSIS_REPORT = "会员数据分析报告"

# 会员总量：total_members
TOTAL_MEMBERS = 15680

# 新增会员数量：new_members
NEW_MEMBERS = 2340

# 历史会员数量（会员总量-新增会员）：historical_members
HISTORICAL_MEMBERS = TOTAL_MEMBERS - NEW_MEMBERS

# 完善手机号的会员数量：complete_phone_members
COMPLETE_PHONE_MEMBERS = 14520

# 未完善手机号的会员数量（会员总量-完善手机号的会员数量）：complete_nophone_members
COMPLETE_NOPHONE_MEMBERS = TOTAL_MEMBERS - COMPLETE_PHONE_MEMBERS

# 完善资料的会员总数：total_complete_members
TOTAL_COMPLETE_MEMBERS = 15200

# 未完善资料的会员数量（会员总量-完善资料的会员总数）：total_nocomplete_members
TOTAL_NOCOMPLETE_MEMBERS = TOTAL_MEMBERS - TOTAL_COMPLETE_MEMBERS

# 消费会员总数：total_consume_members
TOTAL_CONSUME_MEMBERS = 8960

# 无消费的会员数量（会员总量-消费会员总数）：total_noconsume_members
TOTAL_NOCONSUME_MEMBERS = TOTAL_MEMBERS - TOTAL_CONSUME_MEMBERS

# 充值会员总数：total_charge_members
TOTAL_CHARGE_MEMBERS = 5680

# 未充值的会员数量（会员总量-充值会员总数）：total_nocharge_members
TOTAL_NOCHARGE_MEMBERS = TOTAL_MEMBERS - TOTAL_CHARGE_MEMBERS

# 图片参数（参数名必须以 image_ 开头）：image_test
TEST_IMAGE = "./data_reports/images/test.PNG"

# ==================== PPT第8页 - 会员收入分析报告 ====================

# 会员收入分析报告标题：member_revenue_analysis_report
MEMBER_REVENUE_ANALYSIS_REPORT = "会员收入分析报告"

# 会员总实际消费金额：total_actual_amount
TOTAL_ACTUAL_AMOUNT = 1405632.50

# 会员使用储值的实收金额：prepay_actual_amount
PREPAY_ACTUAL_AMOUNT = 892345.60

# 会员实际消费总金额（元）：actual_amount
ACTUAL_AMOUNT = 1405632.50

# 消费频次（平均每个会员的消费次数）：consume_frequency
CONSUME_FREQUENCY = 3.2

# 平均消费金额（元）：avg_consume_amount
AVG_CONSUME_AMOUNT = 156.80

# 有消费行为的用户数：consume_users
CONSUME_USERS = 8960

# 首次消费金额：first_consume_amount
FIRST_CONSUME_AMOUNT = 234567.80

# 重复消费金额：repeat_consume_amount
REPEAT_CONSUME_AMOUNT = 1171064.70

# ==================== PPT第18页 - 优惠券数据分析 ====================

# 平均使用率（%）：avg_usage_rate
AVG_USAGE_RATE = 68.5

# 总驱动金额(优惠券)：total_drive_amount
TOTAL_DRIVE_AMOUNT = 2156789.40

# 优惠券1：coupon_id1, coupon_name1, coupon_send_count1, coupon_used_count1, coupon_usage_rate1, drive_total_amount1
COUPON_ID1 = "CPN001"
COUPON_NAME1 = "新用户专享券"
COUPON_SEND_COUNT1 = 5000
COUPON_USED_COUNT1 = 3200
COUPON_USAGE_RATE1 = 64.0
DRIVE_TOTAL_AMOUNT1 = 128000.00

# 优惠券2：coupon_id2, coupon_name2, coupon_send_count2, coupon_used_count2, coupon_usage_rate2, drive_total_amount2
COUPON_ID2 = "CPN002"
COUPON_NAME2 = "满减优惠券"
COUPON_SEND_COUNT2 = 8000
COUPON_USED_COUNT2 = 4800
COUPON_USAGE_RATE2 = 60.0
DRIVE_TOTAL_AMOUNT2 = 192000.00

# 优惠券3：coupon_id3, coupon_name3, coupon_send_count3, coupon_used_count3, coupon_usage_rate3, drive_total_amount3
COUPON_ID3 = "CPN003"
COUPON_NAME3 = "生日特惠券"
COUPON_SEND_COUNT3 = 3000
COUPON_USED_COUNT3 = 2100
COUPON_USAGE_RATE3 = 70.0
DRIVE_TOTAL_AMOUNT3 = 84000.00

# 优惠券4：coupon_id4, coupon_name4, coupon_send_count4, coupon_used_count4, coupon_usage_rate4, drive_total_amount4
COUPON_ID4 = "CPN004"
COUPON_NAME4 = "节日促销券"
COUPON_SEND_COUNT4 = 12000
COUPON_USED_COUNT4 = 7200
COUPON_USAGE_RATE4 = 60.0
DRIVE_TOTAL_AMOUNT4 = 288000.00

# 优惠券5：coupon_id5, coupon_name5, coupon_send_count5, coupon_used_count5, coupon_usage_rate5, drive_total_amount5
COUPON_ID5 = "CPN005"
COUPON_NAME5 = "会员专属券"
COUPON_SEND_COUNT5 = 6000
COUPON_USED_COUNT5 = 4200
COUPON_USAGE_RATE5 = 70.0
DRIVE_TOTAL_AMOUNT5 = 168000.00

# 优惠券6：coupon_id6, coupon_name6, coupon_send_count6, coupon_used_count6, coupon_usage_rate6, drive_total_amount6
COUPON_ID6 = "CPN006"
COUPON_NAME6 = "限时抢购券"
COUPON_SEND_COUNT6 = 4000
COUPON_USED_COUNT6 = 2800
COUPON_USAGE_RATE6 = 70.0
DRIVE_TOTAL_AMOUNT6 = 112000.00

# 优惠券7：coupon_id7, coupon_name7, coupon_send_count7, coupon_used_count7, coupon_usage_rate7, drive_total_amount7
COUPON_ID7 = "CPN007"
COUPON_NAME7 = "复购奖励券"
COUPON_SEND_COUNT7 = 7000
COUPON_USED_COUNT7 = 4900
COUPON_USAGE_RATE7 = 70.0
DRIVE_TOTAL_AMOUNT7 = 196000.00

# 优惠券8：coupon_id8, coupon_name8, coupon_send_count8, coupon_used_count8, coupon_usage_rate8, drive_total_amount8
COUPON_ID8 = "CPN008"
COUPON_NAME8 = "推荐好友券"
COUPON_SEND_COUNT8 = 2000
COUPON_USED_COUNT8 = 1200
COUPON_USAGE_RATE8 = 60.0
DRIVE_TOTAL_AMOUNT8 = 48000.00

# 优惠券汇总数据：total_send_count, total_used_count
TOTAL_SEND_COUNT = (COUPON_SEND_COUNT1 + COUPON_SEND_COUNT2 + COUPON_SEND_COUNT3 +
                   COUPON_SEND_COUNT4 + COUPON_SEND_COUNT5 + COUPON_SEND_COUNT6 +
                   COUPON_SEND_COUNT7 + COUPON_SEND_COUNT8)

TOTAL_USED_COUNT = (COUPON_USED_COUNT1 + COUPON_USED_COUNT2 + COUPON_USED_COUNT3 +
                   COUPON_USED_COUNT4 + COUPON_USED_COUNT5 + COUPON_USED_COUNT6 +
                   COUPON_USED_COUNT7 + COUPON_USED_COUNT8)

# ==================== 图片配置 ====================

# 图片文件目录
IMAGES_DIR = "./data_reports/images/"

# 图片文件路径（参数名必须以 image_ 开头）
# image_logo, image_chart, image_trend, image_revenue, image_coupon, image_test
COMPANY_LOGO = "./data_reports/images/company_logo.png"
DATA_CHART = "./data_reports/images/data_chart.png"
MEMBER_TREND = "./data_reports/images/member_trend.png"
REVENUE_CHART = "./data_reports/images/revenue_chart.png"
COUPON_ANALYSIS = "./data_reports/images/coupon_analysis.png"

# ==================== 数据字典（为兼容性保留） ====================
# 根据用户要求，主要参数按PPT页码分组组织
# 此字典仅为保持与现有代码的兼容性而保留

MEMBER_DATA = {
    # PPT第1页 - 基础信息
    "time_frame": TIME_FRAME,
    "report_date": REPORT_DATE,

    # PPT第6页 - 会员数据
    "member_data_analysis_report": MEMBER_DATA_ANALYSIS_REPORT,
    "total_members": str(TOTAL_MEMBERS),
    "new_members": str(NEW_MEMBERS),
    "historical_members": str(HISTORICAL_MEMBERS),
    "complete_phone_members": str(COMPLETE_PHONE_MEMBERS),
    "complete_nophone_members": str(COMPLETE_NOPHONE_MEMBERS),
    "total_complete_members": str(TOTAL_COMPLETE_MEMBERS),
    "total_nocomplete_members": str(TOTAL_NOCOMPLETE_MEMBERS),
    "total_consume_members": str(TOTAL_CONSUME_MEMBERS),
    "total_noconsume_members": str(TOTAL_NOCONSUME_MEMBERS),
    "total_charge_members": str(TOTAL_CHARGE_MEMBERS),
    "total_nocharge_members": str(TOTAL_NOCHARGE_MEMBERS),
    "image_test": TEST_IMAGE,

    # PPT第8页 - 收入分析
    "member_revenue_analysis_report": MEMBER_REVENUE_ANALYSIS_REPORT,
    "total_actual_amount": f"{TOTAL_ACTUAL_AMOUNT:,.2f}",
    "prepay_actual_amount": f"{PREPAY_ACTUAL_AMOUNT:,.2f}",
    "actual_amount": f"{ACTUAL_AMOUNT:,.2f}",
    "consume_frequency": str(CONSUME_FREQUENCY),
    "avg_consume_amount": str(AVG_CONSUME_AMOUNT),
    "consume_users": str(CONSUME_USERS),
    "first_consume_amount": f"{FIRST_CONSUME_AMOUNT:,.2f}",
    "repeat_consume_amount": f"{REPEAT_CONSUME_AMOUNT:,.2f}",

    # PPT第18页 - 优惠券数据
    "avg_usage_rate": f"{AVG_USAGE_RATE}%",
    "total_drive_amount": f"{TOTAL_DRIVE_AMOUNT:,.2f}",
    "total_send_count": str(TOTAL_SEND_COUNT),
    "total_used_count": str(TOTAL_USED_COUNT),

    # 优惠券详细数据
    "coupon_id1": COUPON_ID1,
    "coupon_name1": COUPON_NAME1,
    "coupon_send_count1": str(COUPON_SEND_COUNT1),
    "coupon_used_count1": str(COUPON_USED_COUNT1),
    "coupon_usage_rate1": f"{COUPON_USAGE_RATE1}%",
    "drive_total_amount1": f"{DRIVE_TOTAL_AMOUNT1:,.2f}",

    "coupon_id2": COUPON_ID2,
    "coupon_name2": COUPON_NAME2,
    "coupon_send_count2": str(COUPON_SEND_COUNT2),
    "coupon_used_count2": str(COUPON_USED_COUNT2),
    "coupon_usage_rate2": f"{COUPON_USAGE_RATE2}%",
    "drive_total_amount2": f"{DRIVE_TOTAL_AMOUNT2:,.2f}",

    "coupon_id3": COUPON_ID3,
    "coupon_name3": COUPON_NAME3,
    "coupon_send_count3": str(COUPON_SEND_COUNT3),
    "coupon_used_count3": str(COUPON_USED_COUNT3),
    "coupon_usage_rate3": f"{COUPON_USAGE_RATE3}%",
    "drive_total_amount3": f"{DRIVE_TOTAL_AMOUNT3:,.2f}",

    "coupon_id4": COUPON_ID4,
    "coupon_name4": COUPON_NAME4,
    "coupon_send_count4": str(COUPON_SEND_COUNT4),
    "coupon_used_count4": str(COUPON_USED_COUNT4),
    "coupon_usage_rate4": f"{COUPON_USAGE_RATE4}%",
    "drive_total_amount4": f"{DRIVE_TOTAL_AMOUNT4:,.2f}",

    "coupon_id5": COUPON_ID5,
    "coupon_name5": COUPON_NAME5,
    "coupon_send_count5": str(COUPON_SEND_COUNT5),
    "coupon_used_count5": str(COUPON_USED_COUNT5),
    "coupon_usage_rate5": f"{COUPON_USAGE_RATE5}%",
    "drive_total_amount5": f"{DRIVE_TOTAL_AMOUNT5:,.2f}",

    "coupon_id6": COUPON_ID6,
    "coupon_name6": COUPON_NAME6,
    "coupon_send_count6": str(COUPON_SEND_COUNT6),
    "coupon_used_count6": str(COUPON_USED_COUNT6),
    "coupon_usage_rate6": f"{COUPON_USAGE_RATE6}%",
    "drive_total_amount6": f"{DRIVE_TOTAL_AMOUNT6:,.2f}",

    "coupon_id7": COUPON_ID7,
    "coupon_name7": COUPON_NAME7,
    "coupon_send_count7": str(COUPON_SEND_COUNT7),
    "coupon_used_count7": str(COUPON_USED_COUNT7),
    "coupon_usage_rate7": f"{COUPON_USAGE_RATE7}%",
    "drive_total_amount7": f"{DRIVE_TOTAL_AMOUNT7:,.2f}",

    "coupon_id8": COUPON_ID8,
    "coupon_name8": COUPON_NAME8,
    "coupon_send_count8": str(COUPON_SEND_COUNT8),
    "coupon_used_count8": str(COUPON_USED_COUNT8),
    "coupon_usage_rate8": f"{COUPON_USAGE_RATE8}%",
    "drive_total_amount8": f"{DRIVE_TOTAL_AMOUNT8:,.2f}",

    # 图片参数（参数名必须以 image_ 开头）
    "image_logo": COMPANY_LOGO,
    "image_chart": DATA_CHART,
    "image_trend": MEMBER_TREND,
    "image_revenue": REVENUE_CHART,
    "image_coupon": COUPON_ANALYSIS,
}

# ==================== 文件路径配置 ====================

# 模板文件路径（更新为主项目路径）
TEMPLATE_PATH = "../../../ppt_template/会员数据报告-模板.pptx"

# 输出目录（生成的文件将保存在主项目的模板目录下）
OUTPUT_DIR = "../../../ppt_template/"

# 输出文件名前缀（不包含扩展名）
OUTPUT_PREFIX = "会员数据报告"

# 时间戳格式（用于文件命名）
TIMESTAMP_FORMAT = "%Y%m%d_%H%M%S"

# ==================== 辅助函数 ====================

def get_output_filename():
    """生成带时间戳的输出文件名"""
    timestamp = datetime.datetime.now().strftime(TIMESTAMP_FORMAT)
    return f"{OUTPUT_PREFIX}_{timestamp}.pptx"

def get_full_output_path():
    """获取完整的输出文件路径"""
    import os
    filename = get_output_filename()
    return os.path.join(OUTPUT_DIR, filename)

def print_data_summary():
    """打印数据摘要"""
    print("=" * 60)
    print("📊 会员数据报告 - 数据摘要")
    print("=" * 60)
    print(f"📅 时间范围: {TIME_FRAME}")
    print(f"👥 会员总量: {TOTAL_MEMBERS:,}")
    print(f"🆕 新增会员: {NEW_MEMBERS:,}")
    print(f"📱 完善手机号会员: {COMPLETE_PHONE_MEMBERS:,}")
    print(f"💰 总消费金额: ¥{TOTAL_ACTUAL_AMOUNT:,.2f}")
    print(f"🛒 消费会员数: {TOTAL_CONSUME_MEMBERS:,}")
    print(f"🎫 优惠券发放总数: {TOTAL_SEND_COUNT:,}")
    print(f"✅ 优惠券使用总数: {TOTAL_USED_COUNT:,}")
    print(f"📈 优惠券整体使用率: {(TOTAL_USED_COUNT/TOTAL_SEND_COUNT*100):.1f}%")
    print("=" * 60)

def validate_data():
    """验证数据的合理性"""
    errors = []
    warnings = []

    # 基础数据验证
    if NEW_MEMBERS > TOTAL_MEMBERS:
        errors.append("新增会员数不能大于会员总量")

    if HISTORICAL_MEMBERS != (TOTAL_MEMBERS - NEW_MEMBERS):
        warnings.append("历史会员数计算可能有误")

    if TOTAL_CONSUME_MEMBERS > TOTAL_MEMBERS:
        errors.append("消费会员数不能大于会员总量")

    # 优惠券数据验证
    for i in range(1, 9):
        used_count = globals()[f'COUPON_USED_COUNT{i}']
        send_count = globals()[f'COUPON_SEND_COUNT{i}']
        usage_rate = globals()[f'COUPON_USAGE_RATE{i}']

        if used_count > send_count:
            errors.append(f"优惠券{i}使用数量不能大于发放数量")

        calculated_rate = (used_count / send_count * 100) if send_count > 0 else 0
        if abs(calculated_rate - usage_rate) > 1:  # 允许1%的误差
            warnings.append(f"优惠券{i}使用率计算可能有误: 计算值{calculated_rate:.1f}% vs 设定值{usage_rate}%")

    return errors, warnings

def print_validation_report():
    """打印数据验证报告"""
    errors, warnings = validate_data()

    print("\n🔍 数据验证报告")
    print("-" * 40)

    if not errors and not warnings:
        print("✅ 所有数据验证通过")
    else:
        if errors:
            print("❌ 发现错误:")
            for error in errors:
                print(f"   • {error}")

        if warnings:
            print("⚠️  发现警告:")
            for warning in warnings:
                print(f"   • {warning}")

    print("-" * 40)

if __name__ == "__main__":
    # 直接运行此文件可以查看数据摘要和验证报告
    print_data_summary()
    print_validation_report()
