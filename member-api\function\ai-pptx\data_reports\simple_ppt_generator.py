# -*- coding: utf-8 -*-
"""
简化的PPT生成器
专门用于数据报告，不依赖LLM，直接进行参数替换
"""

import os
import re
import shutil
import logging
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.shapes import MSO_SHAPE_TYPE

logger = logging.getLogger(__name__)

class SimplePptGenerator:
    """简化的PPT生成器，用于直接参数替换"""

    PPT_PARAM_PATTERN = r'\{(.*?)\}'
    IMAGE_PARAM_PREFIX = "image_"  # 图片参数前缀
    
    def __init__(self, template_path: str, output_path: str):
        """
        初始化生成器
        
        Args:
            template_path: 模板文件路径
            output_path: 输出文件路径
        """
        self.template_path = template_path
        self.output_path = output_path
        
        # 检查模板文件是否存在
        if not os.path.exists(template_path):
            raise FileNotFoundError(f"模板文件不存在: {template_path}")
    
    def extract_template_params(self):
        """
        从模板中提取所有参数
        
        Returns:
            set: 模板中所有的参数名集合
        """
        try:
            ppt = Presentation(self.template_path)
            params = set()
            
            for slide in ppt.slides:
                for shape in slide.shapes:
                    if shape.has_text_frame:
                        for paragraph in shape.text_frame.paragraphs:
                            for run in paragraph.runs:
                                matches = re.findall(self.PPT_PARAM_PATTERN, run.text)
                                params.update(matches)
            
            return params
        except Exception as e:
            logger.error(f"提取模板参数失败: {e}")
            return set()
    
    def generate_report(self, data_dict: dict):
        """
        生成数据报告
        
        Args:
            data_dict: 包含所有替换数据的字典
        
        Returns:
            bool: 生成是否成功
        """
        try:
            # 1. 复制模板文件到输出路径
            logger.info(f"复制模板文件: {self.template_path} -> {self.output_path}")
            shutil.copy2(self.template_path, self.output_path)
            
            # 2. 打开复制的文件进行编辑
            logger.info("开始替换参数...")
            ppt = Presentation(self.output_path)
            
            # 3. 遍历所有幻灯片进行参数替换
            replaced_count = 0
            image_replaced_count = 0

            for slide_idx, slide in enumerate(ppt.slides):
                # 收集需要删除的形状（图片占位符）
                shapes_to_remove = []
                images_to_add = []

                for shape in slide.shapes:
                    if shape.has_text_frame:
                        for paragraph in shape.text_frame.paragraphs:
                            for run in paragraph.runs:
                                original_text = run.text
                                new_text = original_text

                                # 查找并替换所有参数
                                for match in re.findall(self.PPT_PARAM_PATTERN, original_text):
                                    param_placeholder = "{" + match + "}"

                                    # 检查是否是图片参数
                                    if match.startswith(self.IMAGE_PARAM_PREFIX):
                                        if match in data_dict:
                                            image_path = data_dict[match]
                                            if os.path.exists(image_path):
                                                # 记录图片信息和形状位置
                                                images_to_add.append({
                                                    'path': image_path,
                                                    'left': shape.left,
                                                    'top': shape.top,
                                                    'width': shape.width,
                                                    'height': shape.height
                                                })
                                                shapes_to_remove.append(shape)
                                                image_replaced_count += 1
                                                logger.info(f"幻灯片{slide_idx+1}: 图片参数 {param_placeholder} -> {image_path}")
                                            else:
                                                logger.warning(f"图片文件不存在: {image_path}")
                                        else:
                                            logger.warning(f"未找到图片参数 '{match}' 的路径")
                                    else:
                                        # 普通文本参数
                                        if match in data_dict:
                                            replacement_value = str(data_dict[match])
                                            new_text = new_text.replace(param_placeholder, replacement_value)
                                            replaced_count += 1
                                            logger.info(f"幻灯片{slide_idx+1}: {param_placeholder} -> {replacement_value}")
                                        else:
                                            logger.warning(f"未找到参数 '{match}' 的替换值")

                                # 更新文本
                                if new_text != original_text:
                                    run.text = new_text

                # 删除图片占位符形状
                for shape in shapes_to_remove:
                    self._remove_shape(slide, shape)

                # 添加图片
                for img_info in images_to_add:
                    try:
                        slide.shapes.add_picture(
                            img_info['path'],
                            img_info['left'],
                            img_info['top'],
                            img_info['width'],
                            img_info['height']
                        )
                        logger.info(f"成功插入图片: {img_info['path']}")
                    except Exception as e:
                        logger.error(f"插入图片失败 {img_info['path']}: {e}")
            
            # 4. 保存文件
            logger.info(f"保存文件: {self.output_path}")
            ppt.save(self.output_path)

            logger.info(f"报告生成成功! 共替换了 {replaced_count} 个文本参数，{image_replaced_count} 个图片参数")
            return True
            
        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            return False
    
    def validate_template_params(self, data_dict: dict):
        """
        验证模板参数和数据字典的匹配情况
        
        Args:
            data_dict: 数据字典
        
        Returns:
            tuple: (missing_params, extra_params)
        """
        template_params = self.extract_template_params()
        data_params = set(data_dict.keys())
        
        missing_params = template_params - data_params  # 模板中有但数据中没有的参数
        extra_params = data_params - template_params    # 数据中有但模板中没有的参数
        
        return missing_params, extra_params
    
    def print_validation_report(self, data_dict: dict):
        """打印参数验证报告"""
        missing, extra = self.validate_template_params(data_dict)
        
        print("\n" + "=" * 50)
        print("参数验证报告")
        print("=" * 50)
        
        template_params = self.extract_template_params()
        print(f"模板中的参数: {sorted(template_params)}")
        print(f"数据中的参数: {sorted(data_dict.keys())}")
        
        if missing:
            print(f"⚠️  缺少的参数: {sorted(missing)}")
        else:
            print("✅ 所有模板参数都有对应的数据")
        
        if extra:
            print(f"ℹ️  额外的参数: {sorted(extra)}")
        
        print("=" * 50)

    def _remove_shape(self, slide, shape):
        """
        从幻灯片中删除指定的形状

        Args:
            slide: 幻灯片对象
            shape: 要删除的形状对象
        """
        try:
            # 获取形状在幻灯片中的索引
            shape_element = shape.element
            shape_element.getparent().remove(shape_element)
        except Exception as e:
            logger.warning(f"删除形状失败: {e}")

    def create_image_placeholder_guide(self):
        """
        创建图片占位符使用指南

        Returns:
            str: 使用指南文本
        """
        guide = """
图片参数使用指南:
================

1. 在PPT模板中创建文本框
2. 在文本框中输入图片参数，格式: {image_参数名}
   例如: {image_logo}, {image_chart}, {image_photo}

3. 在配置文件中添加对应的图片路径:
   MEMBER_DATA = {
       "image_logo": "./images/company_logo.png",
       "image_chart": "./images/data_chart.jpg",
       ...
   }

4. 运行生成脚本，文本框将被替换为对应的图片

注意事项:
- 图片参数必须以 'image_' 开头
- 图片文件必须存在于指定路径
- 支持常见图片格式: PNG, JPG, JPEG, GIF, BMP
- 图片将按照文本框的大小和位置进行插入
"""
        return guide
