import logging
import sys

def setup_logging():
    """
    设置日志配置
    """
    # 创建logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # 创建控制台处理器
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.INFO)

    # 设置日志格式
    formatter = logging.Formatter(
        '%(asctime)s [%(levelname)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    handler.setFormatter(formatter)

    # 添加处理器到logger
    logger.addHandler(handler) 