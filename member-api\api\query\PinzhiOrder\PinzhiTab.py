from fastapi import HTTPException, Depends
from typing import Optional, Dict, Any, List
import logging
from datetime import datetime, timedelta
import asyncio
import time
import importlib

from core.database import db
from core.models import QueryParams, PinzhiCashierData, FieldDataModel
from constant import PINZHI_CASHIER_MODULE
from api.query.MemberConsumeTab import MemberConsumeService

# 导入品智收银品牌验证常量和函数
from api.query.PinzhiOrder.PinZhiConstant import (
    validate_bid_for_pinzhi,
    get_brand_info_by_bid,
    get_validation_error_message
)

# 强制重新导入PinzhiSql模块以确保获取最新的方法
import api.query.PinzhiOrder.PinzhiSql as pinzhi_sql_module
importlib.reload(pinzhi_sql_module)
from api.query.PinzhiOrder.PinzhiSql import PinzhiCashierSqlQueries, PinzhiCashierCalculator

# 详细日志记录器（输出到文件）
logger = logging.getLogger(__name__)

# 简要信息记录器（输出到终端）
console_logger = logging.getLogger('startup')

class PinzhiCashierService:
    """品智收银数据服务"""

    def __init__(self):
        self.module_config = PINZHI_CASHIER_MODULE
        self.sql_queries = PinzhiCashierSqlQueries()
        self.calculator = PinzhiCashierCalculator()

        # 创建会员消费服务实例，用于获取会员相关数据
        self.member_consume_service = MemberConsumeService()

    async def _get_member_data(self, start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> Dict[str, Any]:
        """获取会员相关数据，使用会员消费服务的完整数据获取和计算逻辑"""
        try:
            logger.info(f"开始获取会员数据 - bid: {bid}, sid: {sid}, 时间范围: {start_date} 到 {end_date}")

            # 转换日期格式：从YYYY-MM-DD转为YYYYMMDD
            start_date_formatted = start_date.replace('-', '')
            end_date_formatted = end_date.replace('-', '')

            # 调用会员消费服务获取完整的消费数据（包含所有计算逻辑）
            member_consume_data = await self.member_consume_service._fetch_member_consume_data(
                start_date_formatted, end_date_formatted, bid, sid
            )

            # 提取需要的字段
            # 会员总实收金额：使用计算好的 total_real_income 字段
            member_total_actual_amount = member_consume_data.get('total_real_income', 0)
            # 会员总消费笔数：使用从汇总表统计的 total_consume_pv 字段（与品智显示一致）
            member_total_consume_count = member_consume_data.get('total_consume_pv', 0)

            logger.info(f"会员数据获取成功 - 总实收金额: {member_total_actual_amount}, 总消费笔数: {member_total_consume_count}")

            return {
                'member_total_actual_amount': member_total_actual_amount,
                'member_total_consume_count': member_total_consume_count
            }

        except Exception as e:
            logger.error(f"获取会员数据失败: {str(e)}", exc_info=True)
            return {
                'member_total_actual_amount': 0,
                'member_total_consume_count': 0
            }



    async def get_pinzhi_cashier_data(self, query_params: QueryParams) -> Dict[str, Any]:
        """获取品智收银数据

        Args:
            query_params: 查询参数，包含品牌ID、门店ID、时间范围等

        Returns:
            Dict: 品智收银数据字典
        """
        try:
            # 终端显示简要信息
            console_logger.info(f"品智收银数据查询开始 - 商户: {query_params.merchant_id}")

            logger.info(f"=== 品智收银数据获取开始 ===")
            logger.info(f"查询参数: {query_params}")

            # 测试数据库连接状态
            logger.info("=== 开始数据库连接诊断 ===")
            try:
                # 测试数据库连接
                test_result = await db.execute_pos_dw_query("SELECT 1 AS test, current_database() AS db_name, version() AS version")
                if test_result:
                    logger.info(f"数据库连接成功: {test_result[0]}")
                else:
                    logger.error("数据库连接失败：返回空结果")
            except Exception as db_test_error:
                logger.error(f"数据库连接测试失败: {str(db_test_error)}")
                logger.error(f"这说明品智收银数据库配置有问题")
            logger.info("=== 数据库连接诊断结束 ===")

            # 验证必要参数
            if not query_params.bid:
                raise HTTPException(status_code=400, detail="品牌ID不能为空")

            if not query_params.start_date:
                raise HTTPException(status_code=400, detail="开始日期不能为空")

            # 验证收银系统类型
            if getattr(query_params, 'cashier_system', '0') != '1':
                raise HTTPException(status_code=400, detail="当前查询不是品智收银系统")

            # 🔥 新增：验证bid是否支持品智收银系统
            if not validate_bid_for_pinzhi(query_params.bid):
                error_message = get_validation_error_message(query_params.bid)
                logger.error(f"品智收银bid验证失败: {query_params.bid}")
                raise HTTPException(status_code=400, detail=error_message)

            # 记录验证成功的品牌信息
            brand_info = get_brand_info_by_bid(query_params.bid)
            logger.info(f"品智收银bid验证成功: {query_params.bid} -> {brand_info['chinese_name']}")

            # 获取商户ID（对应门店拼音名称）
            merchant_id = getattr(query_params, 'merchant_id', None)
            if not merchant_id:
                raise HTTPException(status_code=400, detail="商户ID不能为空")

            # 获取当前期数据
            logger.info(f"=== 开始获取当前期数据 ===")
            logger.info(f"商户ID: {merchant_id}")
            logger.info(f"开始日期: {query_params.start_date}")
            logger.info(f"结束日期: {query_params.end_date}")

            current_data = await self._get_current_period_data(
                merchant_id,
                query_params.start_date,
                query_params.end_date,
                query_params.bid,
                getattr(query_params, 'sid', None)
            )

            logger.info(f"当前期数据结果: {current_data}")
            logger.info(f"=== 当前期数据获取完成 ===")

            # 获取环比数据
            chain_data = await self._get_chain_comparison_data(
                merchant_id,
                query_params.start_date,
                query_params.end_date,
                query_params.query_type,
                query_params.bid,
                getattr(query_params, 'sid', None)
            )

            # 获取同比数据
            year_over_year_data = await self._get_year_over_year_data(
                merchant_id,
                query_params.start_date,
                query_params.end_date,
                query_params.bid,
                getattr(query_params, 'sid', None)
            )

            # 构建返回数据
            result = self._build_response_data(current_data, chain_data, year_over_year_data)

            # 终端显示成功信息
            console_logger.info("品智收银数据查询完成")

            logger.info("品智收银数据获取成功")
            logger.info(f"=== 品智收银数据获取结束 ===")
            return result

        except HTTPException as e:
            logger.error(f"HTTP异常: {e.detail}")
            raise e
        except Exception as e:
            logger.error(f"获取品智收银数据异常: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

    async def _get_current_period_data(self, merchant_id: str, start_date: str, end_date: str, bid: str = None, sid: str = None) -> Dict[str, Any]:
        """获取当前期数据 - 使用单独的SQL函数获取每个字段"""
        try:
            logger.info(f"=== 开始获取当前期数据 ===")
            logger.info(f"商户ID: {merchant_id}, 时间范围: {start_date} 到 {end_date}")

            # 并行执行所有SQL查询以提高性能
            tasks = []

            # 基础数据查询 - 使用单独的SQL函数
            tasks.append(self._execute_single_query(self.sql_queries.get_total_actual_revenue_sql(merchant_id, start_date, end_date), "营业额总实收"))
            tasks.append(self._execute_single_query(self.sql_queries.get_total_expected_revenue_sql(merchant_id, start_date, end_date), "营业额总应收"))
            tasks.append(self._execute_single_query(self.sql_queries.get_discount_rate_sql(merchant_id, start_date, end_date), "折扣率"))
            tasks.append(self._execute_single_query(self.sql_queries.get_dine_in_actual_revenue_sql(merchant_id, start_date, end_date), "堂食营业额实收"))
            tasks.append(self._execute_single_query(self.sql_queries.get_dine_in_order_count_sql(merchant_id, start_date, end_date), "堂食订单数"))
            tasks.append(self._execute_single_query(self.sql_queries.get_takeout_actual_revenue_sql(merchant_id, start_date, end_date), "外卖营业额实收"))
            tasks.append(self._execute_single_query(self.sql_queries.get_takeout_order_count_sql(merchant_id, start_date, end_date), "外卖订单数"))

            # 新增字段查询 - 使用单独的SQL函数
            tasks.append(self._execute_single_query(self.sql_queries.get_brand_avg_order_value_sql(merchant_id, start_date, end_date), "品牌平均客单价"))
            tasks.append(self._execute_single_query(self.sql_queries.get_dine_in_avg_order_value_sql(merchant_id, start_date, end_date), "堂食平均客单价"))
            tasks.append(self._execute_single_query(self.sql_queries.get_takeout_avg_order_value_sql(merchant_id, start_date, end_date), "外卖平均客单价"))
            tasks.append(self._execute_single_query(self.sql_queries.get_dine_in_revenue_ratio_sql(merchant_id, start_date, end_date), "堂食实收占比"))
            tasks.append(self._execute_single_query(self.sql_queries.get_takeout_revenue_ratio_sql(merchant_id, start_date, end_date), "外卖实收占比"))
            tasks.append(self._execute_single_query(self.sql_queries.get_total_order_count_sql(merchant_id, start_date, end_date), "订单总数"))

            # 执行品智收银相关查询
            results = await asyncio.gather(*tasks, return_exceptions=True)

            logger.info(f"品智收银SQL查询完成，共{len(results)}个查询")

            # 并行获取会员数据（使用正确的数据库连接）
            member_data = await self._get_member_data(start_date, end_date, bid, sid)

            # 提取各字段数据
            total_actual_revenue = float(results[0] or 0)
            total_expected_revenue = float(results[1] or 0)
            discount_rate = float(results[2] or 0)
            dine_in_actual_revenue = float(results[3] or 0)
            dine_in_order_count = int(results[4] or 0)
            takeout_actual_revenue = float(results[5] or 0)
            takeout_order_count = int(results[6] or 0)

            # 新增字段数据
            brand_avg_order_value = float(results[7] or 0)
            dine_in_avg_order_value = float(results[8] or 0)
            takeout_avg_order_value = float(results[9] or 0)
            dine_in_revenue_ratio = float(results[10] or 0)
            takeout_revenue_ratio = float(results[11] or 0)
            total_order_count = int(results[12] or 0)

            # 会员相关字段数据（从会员数据库获取）
            member_total_actual_amount = float(member_data.get('member_total_actual_amount', 0))
            member_total_consume_count = int(member_data.get('member_total_consume_count', 0))

            # 计算衍生字段
            # 非会员总实收金额 = 品智营业额总实收 - 会员总实收金额
            non_member_total_actual_amount = total_actual_revenue - member_total_actual_amount

            # 会员消费金额占堂食实收的比 = 会员总实收金额 / 堂食营业额实收
            member_dine_in_ratio = 0
            if dine_in_actual_revenue > 0:
                member_dine_in_ratio = member_total_actual_amount / dine_in_actual_revenue

            # 会员消费单数占堂食单数的比 = 会员总消费笔数 / 堂食订单数
            member_dine_in_count_ratio = 0
            if dine_in_order_count > 0:
                member_dine_in_count_ratio = member_total_consume_count / dine_in_order_count

            logger.info(f"=== 数据提取完成 ===")
            logger.info(f"营业额总实收: {total_actual_revenue}")
            logger.info(f"营业额总应收: {total_expected_revenue}")
            logger.info(f"折扣率: {discount_rate}")
            logger.info(f"堂食营业额实收: {dine_in_actual_revenue}")
            logger.info(f"堂食订单数: {dine_in_order_count}")
            logger.info(f"外卖营业额实收: {takeout_actual_revenue}")
            logger.info(f"外卖订单数: {takeout_order_count}")
            logger.info(f"品牌平均客单价: {brand_avg_order_value}")
            logger.info(f"堂食平均客单价: {dine_in_avg_order_value}")
            logger.info(f"外卖平均客单价: {takeout_avg_order_value}")
            logger.info(f"堂食实收占比: {dine_in_revenue_ratio}")
            logger.info(f"外卖实收占比: {takeout_revenue_ratio}")
            logger.info(f"订单总数: {total_order_count}")

            return {
                'totalActualRevenue': total_actual_revenue,
                'totalExpectedRevenue': total_expected_revenue,
                'discountRate': discount_rate,
                'dineInActualRevenue': dine_in_actual_revenue,
                'dineInOrderCount': dine_in_order_count,
                'takeoutActualRevenue': takeout_actual_revenue,
                'takeoutOrderCount': takeout_order_count,
                'brandAvgOrderValue': brand_avg_order_value,
                'dineInAvgOrderValue': dine_in_avg_order_value,
                'takeoutAvgOrderValue': takeout_avg_order_value,
                'dineInRevenueRatio': dine_in_revenue_ratio,
                'takeoutRevenueRatio': takeout_revenue_ratio,
                'totalOrderCount': total_order_count,
                # 会员相关字段
                'memberTotalActualAmount': member_total_actual_amount,
                'nonMemberTotalActualAmount': non_member_total_actual_amount,
                'memberDineInRatio': member_dine_in_ratio,
                'memberTotalConsumeCount': member_total_consume_count,
                'memberDineInCountRatio': member_dine_in_count_ratio
            }

        except Exception as e:
            logger.error(f"获取当前期数据失败: {str(e)}", exc_info=True)
            # 返回空数据
            return {
                'totalActualRevenue': 0,
                'totalExpectedRevenue': 0,
                'discountRate': 0,
                'dineInActualRevenue': 0,
                'dineInOrderCount': 0,
                'takeoutActualRevenue': 0,
                'takeoutOrderCount': 0,
                'brandAvgOrderValue': 0,
                'dineInAvgOrderValue': 0,
                'takeoutAvgOrderValue': 0,
                'dineInRevenueRatio': 0,
                'takeoutRevenueRatio': 0,
                'totalOrderCount': 0,
                # 会员相关字段
                'memberTotalActualAmount': 0,
                'nonMemberTotalActualAmount': 0,
                'memberDineInRatio': 0,
                'memberTotalConsumeCount': 0,
                'memberDineInCountRatio': 0
            }



        except Exception as e:
            logger.error(f"获取当前期数据失败: {str(e)}", exc_info=True)
            # 返回空数据
            return {
                'totalActualRevenue': 0,
                'totalExpectedRevenue': 0,
                'discountRate': 0,
                'dineInActualRevenue': 0,
                'dineInOrderCount': 0,
                'takeoutActualRevenue': 0,
                'takeoutOrderCount': 0,
                'brandAvgOrderValue': 0,
                'dineInAvgOrderValue': 0,
                'takeoutAvgOrderValue': 0,
                'dineInRevenueRatio': 0,
                'takeoutRevenueRatio': 0,
                'totalOrderCount': 0
            }

    async def _execute_single_query(self, sql: str, field_name: str) -> float:
        """执行单个SQL查询并返回结果值"""
        try:
            logger.info(f"执行{field_name}查询: {sql}")
            result = await self._execute_pinzhi_sql(sql)

            if not result or len(result) == 0:
                logger.warning(f"{field_name}查询结果为空")
                return 0

            # 获取第一行第一列的值
            value = list(result[0].values())[0] if result[0] else 0
            logger.info(f"{field_name}查询结果: {value}")
            return value

        except Exception as e:
            logger.error(f"执行{field_name}查询失败: {str(e)}", exc_info=True)
            return 0

    async def _get_chain_comparison_data(self, merchant_id: str, start_date: str, end_date: str, query_type: str, bid: str = None, sid: str = None) -> Dict[str, Any]:
        """获取环比数据（上期数据）"""
        try:
            # 计算上期时间范围
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

            # 根据查询类型计算上期时间
            if query_type == 'week':
                prev_start = start_dt - timedelta(days=7)
                prev_end = end_dt - timedelta(days=7)
            elif query_type == 'month':
                prev_start = start_dt - timedelta(days=30)
                prev_end = end_dt - timedelta(days=30)
            elif query_type == 'quarter':
                prev_start = start_dt - timedelta(days=90)
                prev_end = end_dt - timedelta(days=90)
            elif query_type == 'halfyear':
                prev_start = start_dt - timedelta(days=180)
                prev_end = end_dt - timedelta(days=180)
            else:
                # 自定义查询，按时间差计算
                days_diff = (end_dt - start_dt).days + 1
                prev_start = start_dt - timedelta(days=days_diff)
                prev_end = start_dt - timedelta(days=1)

            prev_start_str = prev_start.strftime('%Y-%m-%d')
            prev_end_str = prev_end.strftime('%Y-%m-%d')

            logger.info(f"环比查询时间范围: {prev_start_str} 到 {prev_end_str}")

            # 获取上期数据
            return await self._get_current_period_data(merchant_id, prev_start_str, prev_end_str, bid, sid)

        except Exception as e:
            logger.error(f"获取环比数据失败: {str(e)}")
            # 返回空数据
            return {
                'totalActualRevenue': 0,
                'totalExpectedRevenue': 0,
                'discountRate': 0,
                'dineInActualRevenue': 0,
                'dineInOrderCount': 0,
                'takeoutActualRevenue': 0,
                'takeoutOrderCount': 0,
                'brandAvgOrderValue': 0,
                'dineInAvgOrderValue': 0,
                'takeoutAvgOrderValue': 0,
                'dineInRevenueRatio': 0,
                'takeoutRevenueRatio': 0,
                'totalOrderCount': 0,
                # 会员相关字段
                'memberTotalActualAmount': 0,
                'nonMemberTotalActualAmount': 0,
                'memberDineInRatio': 0,
                'memberTotalConsumeCount': 0,
                'memberDineInCountRatio': 0
            }

    async def _get_year_over_year_data(self, merchant_id: str, start_date: str, end_date: str, bid: str = None, sid: str = None) -> Dict[str, Any]:
        """获取同比数据（去年同期数据）"""
        try:
            # 计算去年同期时间范围
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

            yoy_start = start_dt.replace(year=start_dt.year - 1)
            yoy_end = end_dt.replace(year=end_dt.year - 1)

            yoy_start_str = yoy_start.strftime('%Y-%m-%d')
            yoy_end_str = yoy_end.strftime('%Y-%m-%d')

            logger.info(f"同比查询时间范围: {yoy_start_str} 到 {yoy_end_str}")

            # 获取去年同期数据
            return await self._get_current_period_data(merchant_id, yoy_start_str, yoy_end_str, bid, sid)

        except Exception as e:
            logger.error(f"获取同比数据失败: {str(e)}")
            # 返回空数据
            return {
                'totalActualRevenue': 0,
                'totalExpectedRevenue': 0,
                'discountRate': 0,
                'dineInActualRevenue': 0,
                'dineInOrderCount': 0,
                'takeoutActualRevenue': 0,
                'takeoutOrderCount': 0,
                'brandAvgOrderValue': 0,
                'dineInAvgOrderValue': 0,
                'takeoutAvgOrderValue': 0,
                'dineInRevenueRatio': 0,
                'takeoutRevenueRatio': 0,
                'totalOrderCount': 0,
                # 会员相关字段
                'memberTotalActualAmount': 0,
                'nonMemberTotalActualAmount': 0,
                'memberDineInRatio': 0,
                'memberTotalConsumeCount': 0,
                'memberDineInCountRatio': 0
            }

    def _build_response_data(self, current_data: Dict[str, Any], chain_data: Dict[str, Any], year_over_year_data: Dict[str, Any]) -> Dict[str, Any]:
        """构建返回数据"""
        try:
            result = {}

            # 遍历配置中的每个字段
            for field_name, field_config in self.module_config.items():
                key = field_config['key']

                # 获取当前值
                current_value = current_data.get(key, 0)

                # 获取环比值
                chain_value = chain_data.get(key, 0)

                # 获取同比值
                yoy_value = year_over_year_data.get(key, 0)

                # 计算环比变化率
                if chain_value > 0:
                    chain_rate = ((current_value - chain_value) / chain_value) * 100
                    chain_rate_str = f"{chain_rate:+.2f}%"
                else:
                    chain_rate_str = "-"

                # 计算同比变化率
                if yoy_value > 0:
                    yoy_rate = ((current_value - yoy_value) / yoy_value) * 100
                    yoy_rate_str = f"{yoy_rate:+.2f}%"
                else:
                    yoy_rate_str = "-"

                # 确定单位
                if key in ['discountRate', 'dineInRevenueRatio', 'takeoutRevenueRatio', 'memberDineInRatio', 'memberDineInCountRatio']:
                    # 百分比字段特殊处理：转换为百分比格式
                    unit = '%'
                    current_value = current_value * 100  # 0.8411 -> 84.11
                    chain_value = chain_value * 100
                    yoy_value = yoy_value * 100
                elif 'Count' in key:
                    unit = '单'
                elif 'AvgOrderValue' in key:
                    unit = '元'
                else:
                    unit = '元'

                # 构建字段数据
                result[key] = {
                    'value': current_value,
                    'unit': unit,
                    'chainComparison': [chain_value] if chain_value > 0 else [],
                    'chainChangeRate': [chain_rate_str] if chain_rate_str != "-" else [],
                    'chainLabels': ['上期', '本期'],
                    'yearOverYear': yoy_value,
                    'yearOverYearRate': yoy_rate_str
                }

            return result

        except Exception as e:
            logger.error(f"构建返回数据失败: {str(e)}")
            raise e

    async def _execute_pinzhi_sql(self, sql: str) -> List[Dict[str, Any]]:
        """执行品智收银SQL查询

        连接到PostgreSQL品质收银数据库执行查询
        """
        try:
            logger.info(f"执行品智收银SQL查询: {sql}")

            # 使用品质收银数据库查询方法
            result = await db.execute_pos_dw_query(sql)

            logger.info(f"品智收银SQL查询成功，返回{len(result)}条记录")
            return result

        except Exception as e:
            logger.error(f"执行品智收银SQL查询失败: {str(e)}", exc_info=True)
            # 查询失败时返回空结果，让上层处理
            return []


# 创建服务实例
pinzhi_cashier_service = PinzhiCashierService()