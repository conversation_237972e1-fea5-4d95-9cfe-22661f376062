"""
会员等级数据SQL查询模块
将每个SQL查询拆分成独立的函数，便于维护和独立管理
使用welife_hydb数据库进行查询
"""

from typing import Optional
import logging

logger = logging.getLogger(__name__)

class MemberLevelsSqlQueries:
    """会员等级数据SQL查询类 - 每个查询都是独立的函数"""

    # ========== welife_hydb数据库会员等级数据查询 ==========

    @staticmethod
    def get_per_capita_consumption_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取人均消费额查询SQL

        数据库：welife_hydb
        表：welifehy_trade_consumes, welifehy_welife_users, welifehy_welife_card_categories
        计算方式：消费金额 / 消费会员数
        说明：按会员等级分组计算人均消费额

        Args:
            start_date: 开始日期 (YYYY-MM-DD格式)
            end_date: 结束日期 (YYYY-MM-DD格式)
            bid: 品牌ID
            sid: 门店ID (可选，支持多个门店用逗号分隔)

        Returns:
            人均消费额查询SQL
        """
        # 处理门店ID条件
        if sid:
            sid_list = [s.strip() for s in sid.split(',')]
            sid_condition = f"AND a.sid IN ({','.join(sid_list)})"
        else:
            sid_condition = ""

        return f"""
        WITH t1 AS (
            SELECT
                a.uid,
                a.sid,
                a.grid AS ccids,
                (
                    COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN a.tcTotalFee END), 0)
                    - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN a.tcTotalFee END), 0)
                ) / 100 AS tcTotalFee,
                COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN 1 END), 0)
                - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN 1 END), 0) AS num
            FROM welifehy_trade_consumes a
            JOIN welifehy_welife_users b
                ON a.uid = b.uid AND a.bid = b.bid
            WHERE
                a.bid = {bid}
                AND a.tcstatus = 2
                AND a.uid != 0
                AND a.tcType IN (2, 3, 4, 5, 8)
                AND a.tcSourceType IN (0, 1, 2, 3, 6, 8, 10, 12, 15)
                {sid_condition}
                AND a.tccreated BETWEEN '{start_date} 00:00:00' AND '{end_date} 23:59:59'
            GROUP BY
                a.uid,
                ccids
        ),

        t2 AS (
            SELECT
                ccids,
                sid,
                SUM(CASE WHEN num > 0 THEN 1 END) AS cnum,                            -- 消费会员数
                SUM(tcTotalFee) AS tcTotalFee                                        -- 消费金额
            FROM t1
            GROUP BY
                ccids
        )

        SELECT
            IFNULL(b.ccName, c.ccName) AS ccName,
            sid,
            IFNULL(ROUND(a.tcTotalFee / a.cnum, 2), 0) AS perCapitaConsumption       -- 人均消费额
        FROM t2 a
        LEFT JOIN welifehy_welife_card_categories b
            ON a.ccids = b.ccid
        LEFT JOIN welifehy_welife_card_categories c
            ON c.ccIsCommon = 1 AND c.bid = {bid}
        ORDER BY a.ccids
        """

    @staticmethod
    def get_customer_unit_price_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取客单价查询SQL

        数据库：welife_hydb
        表：welifehy_trade_consumes, welifehy_welife_users, welifehy_welife_card_categories
        计算方式：消费金额 / 消费订单数
        说明：按会员等级分组计算客单价

        Args:
            start_date: 开始日期 (YYYY-MM-DD格式)
            end_date: 结束日期 (YYYY-MM-DD格式)
            bid: 品牌ID
            sid: 门店ID (可选，支持多个门店用逗号分隔)

        Returns:
            客单价查询SQL
        """
        # 处理门店ID条件
        if sid:
            sid_list = [s.strip() for s in sid.split(',')]
            sid_condition = f"AND a.sid IN ({','.join(sid_list)})"
        else:
            sid_condition = ""

        return f"""
        WITH t1 AS (
            SELECT
                a.uid,
                a.sid,
                a.grid AS ccids,
                (
                    COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN a.tcTotalFee END), 0)
                    - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN a.tcTotalFee END), 0)
                ) / 100 AS tcTotalFee,
                COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN 1 END), 0)
                - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN 1 END), 0) AS num
            FROM welifehy_trade_consumes a
            JOIN welifehy_welife_users b
                ON a.uid = b.uid AND a.bid = b.bid
            WHERE
                a.bid = {bid}
                AND a.tcstatus = 2
                AND a.uid != 0
                AND a.tcType IN (2, 3, 4, 5, 8)
                AND a.tcSourceType IN (0, 1, 2, 3, 6, 8, 10, 12, 15)
                {sid_condition}
                AND a.tccreated BETWEEN '{start_date} 00:00:00' AND '{end_date} 23:59:59'
            GROUP BY
                a.uid,
                ccids
        ),

        t2 AS (
            SELECT
                ccids,
                sid,
                SUM(num) AS ccount,                                                  -- 消费订单数
                SUM(tcTotalFee) AS tcTotalFee                                        -- 消费金额
            FROM t1
            GROUP BY
                ccids
        )

        SELECT
            IFNULL(b.ccName, c.ccName) AS ccName,
            sid,
            IFNULL(ROUND(a.tcTotalFee / a.ccount, 2), 0) AS customerUnitPrice        -- 客单价
        FROM t2 a
        LEFT JOIN welifehy_welife_card_categories b
            ON a.ccids = b.ccid
        LEFT JOIN welifehy_welife_card_categories c
            ON c.ccIsCommon = 1 AND c.bid = {bid}
        ORDER BY a.ccids
        """

    @staticmethod
    def get_avg_consumption_frequency_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取平均消费频次查询SQL

        数据库：welife_hydb
        表：welifehy_trade_consumes, welifehy_welife_users, welifehy_welife_card_categories
        计算方式：消费订单数 / 消费会员数
        说明：按会员等级分组计算平均消费频次

        Args:
            start_date: 开始日期 (YYYY-MM-DD格式)
            end_date: 结束日期 (YYYY-MM-DD格式)
            bid: 品牌ID
            sid: 门店ID (可选，支持多个门店用逗号分隔)

        Returns:
            平均消费频次查询SQL
        """
        # 处理门店ID条件
        if sid:
            sid_list = [s.strip() for s in sid.split(',')]
            sid_condition = f"AND a.sid IN ({','.join(sid_list)})"
        else:
            sid_condition = ""

        return f"""
        WITH t1 AS (
            SELECT
                a.uid,
                a.sid,
                a.grid AS ccids,
                (
                    COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN a.tcTotalFee END), 0)
                    - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN a.tcTotalFee END), 0)
                ) / 100 AS tcTotalFee,
                COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN 1 END), 0)
                - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN 1 END), 0) AS num
            FROM welifehy_trade_consumes a
            JOIN welifehy_welife_users b
                ON a.uid = b.uid AND a.bid = b.bid
            WHERE
                a.bid = {bid}
                AND a.tcstatus = 2
                AND a.uid != 0
                AND a.tcType IN (2, 3, 4, 5, 8)
                AND a.tcSourceType IN (0, 1, 2, 3, 6, 8, 10, 12, 15)
                {sid_condition}
                AND a.tccreated BETWEEN '{start_date} 00:00:00' AND '{end_date} 23:59:59'
            GROUP BY
                a.uid,
                ccids
        ),

        t2 AS (
            SELECT
                ccids,
                sid,
                SUM(CASE WHEN num > 0 THEN 1 END) AS cnum,                            -- 消费会员数
                SUM(num) AS ccount                                                   -- 消费订单数
            FROM t1
            GROUP BY
                ccids
        )

        SELECT
            IFNULL(b.ccName, c.ccName) AS ccName,
            sid,
            IFNULL(ROUND(a.ccount / a.cnum, 2), 0) AS avgConsumFrequency             -- 平均消费频次
        FROM t2 a
        LEFT JOIN welifehy_welife_card_categories b
            ON a.ccids = b.ccid
        LEFT JOIN welifehy_welife_card_categories c
            ON c.ccIsCommon = 1 AND c.bid = {bid}
        ORDER BY a.ccids
        """

    @staticmethod
    def get_order_money_ratio_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取消费金额占比查询SQL

        数据库：welife_hydb
        表：welifehy_trade_consumes, welifehy_welife_users, welifehy_welife_card_categories
        计算方式：各等级消费金额 / 总消费金额 * 100%
        说明：按会员等级分组计算消费金额占比

        Args:
            start_date: 开始日期 (YYYY-MM-DD格式)
            end_date: 结束日期 (YYYY-MM-DD格式)
            bid: 品牌ID
            sid: 门店ID (可选，支持多个门店用逗号分隔)

        Returns:
            消费金额占比查询SQL
        """
        # 处理门店ID条件
        if sid:
            sid_list = [s.strip() for s in sid.split(',')]
            sid_condition = f"AND a.sid IN ({','.join(sid_list)})"
        else:
            sid_condition = ""

        return f"""
        WITH t1 AS (
            SELECT
                a.uid,
                a.sid,
                a.grid AS ccids,
                (
                    COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN a.tcTotalFee END), 0)
                    - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN a.tcTotalFee END), 0)
                ) / 100 AS tcTotalFee,
                COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN 1 END), 0)
                - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN 1 END), 0) AS num
            FROM welifehy_trade_consumes a
            JOIN welifehy_welife_users b
                ON a.uid = b.uid AND a.bid = b.bid
            WHERE
                a.bid = {bid}
                AND a.tcstatus = 2
                AND a.uid != 0
                AND a.tcType IN (2, 3, 4, 5, 8)
                AND a.tcSourceType IN (0, 1, 2, 3, 6, 8, 10, 12, 15)
                {sid_condition}
                AND a.tccreated BETWEEN '{start_date} 00:00:00' AND '{end_date} 23:59:59'
            GROUP BY
                a.uid,
                ccids
        ),

        t2 AS (
            SELECT
                ccids,
                sid,
                SUM(tcTotalFee) AS tcTotalFee                                        -- 消费金额
            FROM t1
            GROUP BY
                ccids
        )

        SELECT
            IFNULL(b.ccName, c.ccName) AS ccName,
            sid,
            IFNULL(CONCAT(CAST((a.tcTotalFee / SUM(a.tcTotalFee) OVER()) * 100 AS DECIMAL(10, 2)), '%'), '0.00%') AS orderMoneyRatio  -- 消费金额占比
        FROM t2 a
        LEFT JOIN welifehy_welife_card_categories b
            ON a.ccids = b.ccid
        LEFT JOIN welifehy_welife_card_categories c
            ON c.ccIsCommon = 1 AND c.bid = {bid}
        ORDER BY a.ccids
        """

    @staticmethod
    def get_order_num_ratio_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取消费订单占比查询SQL

        数据库：welife_hydb
        表：welifehy_trade_consumes, welifehy_welife_users, welifehy_welife_card_categories
        计算方式：各等级消费订单数 / 总消费订单数 * 100%
        说明：按会员等级分组计算消费订单占比

        Args:
            start_date: 开始日期 (YYYY-MM-DD格式)
            end_date: 结束日期 (YYYY-MM-DD格式)
            bid: 品牌ID
            sid: 门店ID (可选，支持多个门店用逗号分隔)

        Returns:
            消费订单占比查询SQL
        """
        # 处理门店ID条件
        if sid:
            sid_list = [s.strip() for s in sid.split(',')]
            sid_condition = f"AND a.sid IN ({','.join(sid_list)})"
        else:
            sid_condition = ""

        return f"""
        WITH t1 AS (
            SELECT
                a.uid,
                a.sid,
                a.grid AS ccids,
                (
                    COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN a.tcTotalFee END), 0)
                    - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN a.tcTotalFee END), 0)
                ) / 100 AS tcTotalFee,
                COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN 1 END), 0)
                - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN 1 END), 0) AS num
            FROM welifehy_trade_consumes a
            JOIN welifehy_welife_users b
                ON a.uid = b.uid AND a.bid = b.bid
            WHERE
                a.bid = {bid}
                AND a.tcstatus = 2
                AND a.uid != 0
                AND a.tcType IN (2, 3, 4, 5, 8)
                AND a.tcSourceType IN (0, 1, 2, 3, 6, 8, 10, 12, 15)
                {sid_condition}
                AND a.tccreated BETWEEN '{start_date} 00:00:00' AND '{end_date} 23:59:59'
            GROUP BY
                a.uid,
                ccids
        ),

        t2 AS (
            SELECT
                ccids,
                sid,
                SUM(num) AS ccount                                                   -- 消费订单数
            FROM t1
            GROUP BY
                ccids
        )

        SELECT
            IFNULL(b.ccName, c.ccName) AS ccName,
            sid,
            IFNULL(CONCAT(CAST((a.ccount / SUM(a.ccount) OVER()) * 100 AS DECIMAL(10, 2)), '%'), '0.00%') AS orderNumRatio            -- 消费订单占比
        FROM t2 a
        LEFT JOIN welifehy_welife_card_categories b
            ON a.ccids = b.ccid
        LEFT JOIN welifehy_welife_card_categories c
            ON c.ccIsCommon = 1 AND c.bid = {bid}
        ORDER BY a.ccids
        """

    @staticmethod
    def get_repurchase_rate_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取复购率查询SQL

        数据库：welife_hydb
        表：welifehy_trade_consumes, welifehy_welife_users, welifehy_welife_card_categories
        计算方式：复购会员数 / 消费会员数 * 100%
        说明：按会员等级分组计算复购率（消费次数>1的会员占比）

        Args:
            start_date: 开始日期 (YYYY-MM-DD格式)
            end_date: 结束日期 (YYYY-MM-DD格式)
            bid: 品牌ID
            sid: 门店ID (可选，支持多个门店用逗号分隔)

        Returns:
            复购率查询SQL
        """
        # 处理门店ID条件
        if sid:
            sid_list = [s.strip() for s in sid.split(',')]
            sid_condition = f"AND a.sid IN ({','.join(sid_list)})"
        else:
            sid_condition = ""

        return f"""
        WITH t1 AS (
            SELECT
                a.uid,
                a.sid,
                a.grid AS ccids,
                (
                    COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN a.tcTotalFee END), 0)
                    - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN a.tcTotalFee END), 0)
                ) / 100 AS tcTotalFee,
                COALESCE(SUM(CASE WHEN a.tcType IN (2, 4, 5, 8) THEN 1 END), 0)
                - COALESCE(SUM(CASE WHEN a.tcType IN (3) THEN 1 END), 0) AS num
            FROM welifehy_trade_consumes a
            JOIN welifehy_welife_users b
                ON a.uid = b.uid AND a.bid = b.bid
            WHERE
                a.bid = {bid}
                AND a.tcstatus = 2
                AND a.uid != 0
                AND a.tcType IN (2, 3, 4, 5, 8)
                AND a.tcSourceType IN (0, 1, 2, 3, 6, 8, 10, 12, 15)
                {sid_condition}
                AND a.tccreated BETWEEN '{start_date} 00:00:00' AND '{end_date} 23:59:59'
            GROUP BY
                a.uid,
                ccids
        ),

        t2 AS (
            SELECT
                ccids,
                sid,
                COUNT(DISTINCT CASE WHEN num > 1 THEN uid END) AS ccnum,              -- 复购会员数
                SUM(CASE WHEN num > 0 THEN 1 END) AS cnum                            -- 消费会员数
            FROM t1
            GROUP BY
                ccids
        )

        SELECT
            IFNULL(b.ccName, c.ccName) AS ccName,
            sid,
            IFNULL(CONCAT(CAST((a.ccnum / a.cnum) * 100 AS DECIMAL(10, 2)), '%'), '0.00%') AS repurchaseRate                           -- 复购率
        FROM t2 a
        LEFT JOIN welifehy_welife_card_categories b
            ON a.ccids = b.ccid
        LEFT JOIN welifehy_welife_card_categories c
            ON c.ccIsCommon = 1 AND c.bid = {bid}
        ORDER BY a.ccids
        """