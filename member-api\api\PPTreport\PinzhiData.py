# -*- coding: utf-8 -*-
"""
品智收银数据获取服务
为PPT报告提供品智收银相关数据
"""

import logging
from typing import Dict, Any, Optional

from core.models import QueryParams
from api.query.PinzhiOrder.PinzhiTab import pinzhi_cashier_service

logger = logging.getLogger(__name__)

class PinzhiDataService:
    """品智收银数据服务类"""

    def __init__(self):
        """初始化品智数据服务"""
        self.pinzhi_service = pinzhi_cashier_service
        logger.info("品智数据服务初始化完成")

    async def get_pinzhi_data_for_ppt(self, query_params: QueryParams) -> Dict[str, Any]:
        """
        获取PPT所需的品智收银数据

        Args:
            query_params: 查询参数

        Returns:
            Dict: 品智收银数据，包含PPT第八页所需的7个字段
        """
        try:
            # 检查是否为品智收银系统
            if query_params.cashier_system != '1':
                logger.info("非品智收银系统，返回空数据")
                return self._get_empty_pinzhi_data()

            # 检查是否有商户ID
            if not query_params.merchant_id:
                logger.warning("品智收银系统缺少商户ID，返回空数据")
                return self._get_empty_pinzhi_data()

            logger.info(f"开始获取品智收银数据 - 商户: {query_params.merchant_id}")

            # 调用品智收银服务获取数据
            pinzhi_data = await self.pinzhi_service.get_pinzhi_cashier_data(query_params)

            # 提取PPT所需的字段
            ppt_data = self._extract_ppt_fields(pinzhi_data)

            logger.info(f"品智收银数据获取完成: {ppt_data}")
            return ppt_data

        except Exception as e:
            logger.error(f"获取品智收银数据失败: {str(e)}", exc_info=True)
            return self._get_empty_pinzhi_data()

    def _extract_ppt_fields(self, pinzhi_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        从品智收银数据中提取PPT所需的字段

        Args:
            pinzhi_data: 品智收银原始数据

        Returns:
            Dict: PPT所需的7个字段数据
        """
        try:
            # 映射字段到PPT格式
            ppt_fields = {
                # 营业额实收
                "total_actual_revenue": self._extract_field_value(pinzhi_data, "totalActualRevenue"),
                # 营业额应收
                "total_expected_revenue": self._extract_field_value(pinzhi_data, "totalExpectedRevenue"),
                # 堂食实收
                "dine_in_actual_revenue": self._extract_field_value(pinzhi_data, "dineInActualRevenue"),
                # 外卖实收
                "takeout_actual_revenue": self._extract_field_value(pinzhi_data, "takeoutActualRevenue"),
                # 非会员实收
                "non_member_total_actual_amount": self._extract_field_value(pinzhi_data, "nonMemberTotalActualAmount"),
                # 折扣
                "discount_rate": self._extract_field_value(pinzhi_data, "discountRate"),
                # 会员消费占比
                "member_dine_in_ratio": self._extract_field_value(pinzhi_data, "memberDineInRatio")
            }

            logger.debug(f"提取的PPT字段: {ppt_fields}")
            return ppt_fields

        except Exception as e:
            logger.error(f"提取PPT字段失败: {str(e)}")
            return self._get_empty_pinzhi_data()

    def _extract_field_value(self, data: Dict[str, Any], field_key: str) -> float:
        """
        从数据中提取字段值

        Args:
            data: 数据字典
            field_key: 字段键名

        Returns:
            float: 字段值
        """
        try:
            field_data = data.get(field_key, {})
            if isinstance(field_data, dict):
                return float(field_data.get('value', 0))
            else:
                return float(field_data or 0)
        except (ValueError, TypeError):
            logger.warning(f"字段 {field_key} 值转换失败，使用默认值0")
            return 0.0

    def _get_empty_pinzhi_data(self) -> Dict[str, Any]:
        """
        获取空的品智数据

        Returns:
            Dict: 空的品智数据
        """
        return {
            "total_actual_revenue": 0.0,
            "total_expected_revenue": 0.0,
            "dine_in_actual_revenue": 0.0,
            "takeout_actual_revenue": 0.0,
            "non_member_total_actual_amount": 0.0,
            "discount_rate": 0.0,
            "member_dine_in_ratio": 0.0
        }

# 创建服务实例
pinzhi_data_service = PinzhiDataService()