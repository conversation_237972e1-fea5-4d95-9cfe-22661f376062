# -*- coding: utf-8 -*-
"""
OSS对象存储服务
提供文件上传、下载、删除等基本操作
"""

import logging
import hashlib
import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Union

logger = logging.getLogger(__name__)

class OSSService:
    """OSS对象存储服务类"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化OSS服务

        Args:
            config: OSS配置参数
        """
        self.config = config or self._get_default_config()
        self.base_url = self.config.get("base_url", "http://localhost:8000")
        self.upload_path = Path(self.config.get("upload_path", "./uploads"))
        self.max_file_size = self.config.get("max_file_size", 50 * 1024 * 1024)  # 50MB

        # 确保上传目录存在
        self.upload_path.mkdir(parents=True, exist_ok=True)

        logger.info(f"OSS服务初始化完成 - 上传路径: {self.upload_path}")

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "base_url": "http://localhost:8000",
            "upload_path": "./uploads",
            "max_file_size": 50 * 1024 * 1024,
            "allowed_extensions": [".pptx", ".pdf", ".docx", ".xlsx"],
            "url_expire_hours": 24
        }

    def upload_file(
        self,
        file_path: Union[str, Path],
        object_name: Optional[str] = None,
        folder: str = "ppt-reports"
    ) -> Dict[str, Any]:
        """
        上传文件到OSS

        Args:
            file_path: 本地文件路径
            object_name: 对象名称（可选，默认使用文件名）
            folder: 存储文件夹

        Returns:
            Dict: 上传结果
        """
        try:
            file_path = Path(file_path)

            # 验证文件是否存在
            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"文件不存在: {file_path}",
                    "file_url": None
                }

            # 验证文件大小
            file_size = file_path.stat().st_size
            if file_size > self.max_file_size:
                return {
                    "success": False,
                    "error": f"文件大小超过限制: {file_size / 1024 / 1024:.1f}MB > {self.max_file_size / 1024 / 1024:.1f}MB",
                    "file_url": None
                }

            # 验证文件扩展名
            allowed_extensions = self.config.get("allowed_extensions", [])
            if allowed_extensions and file_path.suffix.lower() not in allowed_extensions:
                return {
                    "success": False,
                    "error": f"不支持的文件类型: {file_path.suffix}",
                    "file_url": None
                }

            # 生成对象名称
            if not object_name:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                file_hash = self._calculate_file_hash(file_path)[:8]
                object_name = f"{file_path.stem}_{timestamp}_{file_hash}{file_path.suffix}"

            # 创建目标路径
            target_folder = self.upload_path / folder
            target_folder.mkdir(parents=True, exist_ok=True)
            target_path = target_folder / object_name

            # 复制文件到目标位置
            import shutil
            shutil.copy2(file_path, target_path)

            # 生成访问URL
            file_url = self._generate_file_url(folder, object_name)

            logger.info(f"文件上传成功: {file_path} -> {target_path}")
            logger.info(f"OSS上传详情:")
            logger.info(f"  - 源文件: {file_path}")
            logger.info(f"  - 目标路径: {target_path}")
            logger.info(f"  - 对象名: {object_name}")
            logger.info(f"  - 文件夹: {folder}")
            logger.info(f"  - 生成的URL: {file_url}")
            logger.info(f"  - base_url: {self.base_url}")

            return {
                "success": True,
                "message": "文件上传成功",
                "file_url": file_url,
                "file_path": str(target_path),  # 添加最终文件路径
                "object_name": object_name,
                "folder": folder,
                "file_size": file_size,
                "upload_time": datetime.datetime.now().isoformat()
            }

        except Exception as e:
            error_msg = f"文件上传失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "file_url": None
            }

    def download_file(self, object_name: str, folder: str = "ppt-reports") -> Dict[str, Any]:
        """
        获取文件下载信息

        Args:
            object_name: 对象名称
            folder: 文件夹

        Returns:
            Dict: 下载信息
        """
        try:
            # 安全检查：防止路径遍历攻击
            if ".." in object_name or ".." in folder:
                return {
                    "success": False,
                    "error": f"无效的文件路径: {folder}/{object_name}",
                    "file_path": None
                }

            # 规范化路径
            file_path = self.upload_path / folder / object_name

            # 确保文件路径在允许的目录内
            try:
                file_path = file_path.resolve()
                upload_path_resolved = self.upload_path.resolve()
                if not str(file_path).startswith(str(upload_path_resolved)):
                    return {
                        "success": False,
                        "error": f"文件路径超出允许范围: {object_name}",
                        "file_path": None
                    }
            except Exception as path_error:
                logger.error(f"路径解析失败: {path_error}")
                return {
                    "success": False,
                    "error": f"路径解析失败: {object_name}",
                    "file_path": None
                }

            if not file_path.exists():
                logger.warning(f"文件不存在: {file_path}")
                return {
                    "success": False,
                    "error": f"文件不存在: {object_name}",
                    "file_path": None
                }

            # 检查文件大小
            file_size = file_path.stat().st_size
            if file_size == 0:
                logger.warning(f"文件大小为0: {file_path}")
                return {
                    "success": False,
                    "error": f"文件为空: {object_name}",
                    "file_path": None
                }

            file_url = self._generate_file_url(folder, object_name)

            logger.info(f"文件下载信息获取成功: {file_path} (大小: {file_size} bytes)")

            return {
                "success": True,
                "file_url": file_url,
                "file_path": str(file_path),
                "file_size": file_size,
                "object_name": object_name,
                "folder": folder
            }

        except Exception as e:
            error_msg = f"获取文件下载信息失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "file_path": None
            }

    def delete_file(self, object_name: str, folder: str = "ppt-reports") -> Dict[str, Any]:
        """
        删除文件

        Args:
            object_name: 对象名称
            folder: 文件夹

        Returns:
            Dict: 删除结果
        """
        try:
            file_path = self.upload_path / folder / object_name

            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"文件不存在: {object_name}"
                }

            file_path.unlink()
            logger.info(f"文件删除成功: {file_path}")

            return {
                "success": True,
                "message": "文件删除成功",
                "object_name": object_name,
                "folder": folder
            }

        except Exception as e:
            error_msg = f"文件删除失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }

    def list_files(self, folder: str = "ppt-reports") -> Dict[str, Any]:
        """
        列出文件夹中的文件

        Args:
            folder: 文件夹

        Returns:
            Dict: 文件列表
        """
        try:
            folder_path = self.upload_path / folder

            if not folder_path.exists():
                return {
                    "success": True,
                    "files": [],
                    "total_count": 0
                }

            files = []
            for file_path in folder_path.iterdir():
                if file_path.is_file():
                    file_info = {
                        "object_name": file_path.name,
                        "file_size": file_path.stat().st_size,
                        "modified_time": datetime.datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                        "file_url": self._generate_file_url(folder, file_path.name)
                    }
                    files.append(file_info)

            return {
                "success": True,
                "files": files,
                "total_count": len(files),
                "folder": folder
            }

        except Exception as e:
            error_msg = f"列出文件失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "files": []
            }

    def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()

    def _generate_file_url(self, folder: str, object_name: str) -> str:
        """生成文件访问URL"""
        # 修复URL路径，确保与router.py中的路由定义一致
        return f"{self.base_url}/api/ppt-report/download/{folder}/{object_name}"

    def get_file_info(self, object_name: str, folder: str = "ppt-reports") -> Dict[str, Any]:
        """
        获取文件信息

        Args:
            object_name: 对象名称
            folder: 文件夹

        Returns:
            Dict: 文件信息
        """
        try:
            file_path = self.upload_path / folder / object_name

            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"文件不存在: {object_name}"
                }

            stat = file_path.stat()

            return {
                "success": True,
                "object_name": object_name,
                "folder": folder,
                "file_size": stat.st_size,
                "file_size_mb": round(stat.st_size / 1024 / 1024, 2),
                "created_time": datetime.datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified_time": datetime.datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "file_url": self._generate_file_url(folder, object_name),
                "file_extension": file_path.suffix.lower()
            }

        except Exception as e:
            error_msg = f"获取文件信息失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }


# 创建全局OSS服务实例
oss_service = OSSService()