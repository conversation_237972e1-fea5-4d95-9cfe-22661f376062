# -*- coding: utf-8 -*-
"""
该文件用于记录所有统计的常量，包括多个模块，基于fieldREAADME.md文件定义的数据字段和计算逻辑
"""

# 会员基础模块常量
MEMBER_BASE_MODULE = {
    "会员总数量": {
        "table": "dprpt_welife_user_log",
        "field": "all_user + all_unregistered_user",
        "logic": "累计会员量 + 取消关注会员量",
        "note": "取范围内最后一天数值加和",
        "key": "totalMembers"
    },
    "会员净存量": {
        "table": "dprpt_welife_user_log", 
        "field": "all_user",
        "logic": "累计会员量",
        "note": "取范围内最后一天数值加和后进行加减法",
        "key": "netMembers"
    },
    "新增会员数量": {
        "table": "dprpt_welife_user_log",
        "field": "new_user", 
        "logic": "新增的注册用户数",
        "note": "加和",
        "key": "newMembers"
    },
    "取关会员数量": {
        "table": "dprpt_welife_user_log",
        "field": "new_unregistered_user",
        "logic": "取消关注会员量", 
        "note": "加和",
        "key": "unfollowMembers"
    },
    "取关占比": {
        "table": "dprpt_welife_user_log",
        "field": "new_unregistered_user / new_user",
        "logic": "取消关注会员量 / 新增的注册用户数",
        "note": "加和后进行除法",
        "key": "unfollowRate"
    },
    "新增消费的会员数": {
        "table": "dprpt_welife_user_log",
        "field": "new_user_consomer",
        "logic": "新增消费会员量",
        "note": "加和",
        "key": "newConsumeMembers"
    },
    "新增储值的会员数": {
        "table": "dprpt_welife_user_log", 
        "field": "new_user_charger",
        "logic": "新增储值会员量",
        "note": "加和",
        "key": "newChargeMembers"
    },
    "累计消费的会员数": {
        "table": "dprpt_welife_user_log",
        "field": "all_user_consomer", 
        "logic": "累计新增消费会员量",
        "note": "取范围内最后一天数值进行加和",
        "key": "totalConsumeMembers"
    },
    "累计储值的会员数": {
        "table": "dprpt_welife_user_log",
        "field": "all_user_charger",
        "logic": "累计新增储值会员量", 
        "note": "取范围内最后一天数值进行加和",
        "key": "totalChargeMembers"
    },
    "新增完善会员量": {
        "table": "dprpt_welife_users_stat",
        "field": "uUserInfoNum",
        "logic": "新增完善四项基础资料会员量",
        "note": "加和",
        "key": "newCompleteMembers"
    },
    "新增会员完善率": {
        "table": "dprpt_welife_users_stat",
        "field": "uUserInfoNum / uIncreNum",
        "logic": "新增完善四项基础资料会员量/ 新增会员量",
        "note": "加和后进行除法",
        "key": "newCompleteRate"
    },
    "完善手机号会员数量": {
        "table": "dprpt_welife_users_stat",
        "field": "uCardPhoneNum",
        "logic": "取范围内最后一天数值加和",
        "note": "加和",
        "key": "completePhoneMembers"
    },
    "完善会员总数": {
        "table": "dprpt_welife_users_stat",
        "field": "uCardInfoNum",
        "logic": "期末四项基础资料会员存量",
        "note": "取范围内最后一天数值进行加和",
        "key": "totalCompleteMembers"
    },
    "完善手机号会员占比": {
        "table": "",
        "field": "完善手机号会员数量/会员总数量",
        "logic": "完善手机号会员数量 / 会员总数量 * 100",
        "note": "计算后除，以百分比显示",
        "key": "phoneMemberRatio"
    },
    "储值会员占比": {
        "table": "",
        "field": "累计储值的会员数/会员总数量",
        "logic": "累计储值的会员数 / 会员总数量 * 100",
        "note": "计算后除，以百分比显示",
        "key": "prepayMemberRatio"
    },
    "会员消费人数占比": {
        "table": "",
        "field": "累计消费的会员数/会员总数量",
        "logic": "累计消费的会员数 / 会员总数量 * 100",
        "note": "计算后除，以百分比显示",
        "key": "consumeMemberRatio"
    },
    "会员1次消费总人数": {
        "table": "dprpt_welife_users_stat",
        "field": "uConsume1Num",
        "logic": "期末历史消费1次会员数量",
        "note": "取范围内最后一天数值进行加和",
        "key": "consumeOnceMembers"
    },
    "会员2次消费以上总人数": {
        "table": "dprpt_welife_users_stat",
        "field": "uConsume2Num",
        "logic": "期末历史消费2次（含）以上会员数量",
        "note": "取范围内最后一天数值进行加和",
        "key": "consumeMultipleMembers"
    }
}

# 会员消费模块常量
MEMBER_CONSUME_MODULE = {
    "消费总金额": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_amount + overdue_amount - cancel_amount",
        "logic": "正常消费 + 逾期消费 - 取消消费",
        "note": "分别进行加和后进行加减法",
        "key": "totalAmount"
    },
    "会员消费人数": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_amount_uv + overdue_uv",
        "logic": "正常消费人数 + 逾期消费人数（取消的用户不减）",
        "note": "分别进行加和后进行加减法",
        "key": "consumeUsers"
    },
    "会员总消费笔数": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_amount_pv + overdue_pv - cancel_amount_pv",
        "logic": "正常消费笔数 + 逾期消费笔数 - 取消消费笔数",
        "note": "分别进行加和后进行加减法",
        "key": "totalConsumeCount"
    },
    "会员实收金额": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_cash - cancel_cash",
        "logic": "现金支付 - 取消现金支付",
        "note": "分别进行加和后进行加减法",
        "key": "actualAmount"
    },
    "现金支付会员数": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_cash_uv",
        "logic": "使用现金支付的用户数（取消不减）",
        "note": "加和",
        "key": "cashUsers"
    },
    "会员现金消费笔数": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_cash_pv - cancel_cash_pv",
        "logic": "现金支付笔数 - 取消现金支付笔数",
        "note": "分别进行加和后进行加减法",
        "key": "cashConsumeCount"
    },
    "会员储值消费金额": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_prepay - cancel_prepay + overdue_amount",
        "logic": "储值预存 - 取消储值预存 + 逾期金额",
        "note": "分别进行加和后进行加减法",
        "key": "prepayAmount"
    },
    "会员使用储值的实收金额": {
        "table": "dprpt_welife_trade_consume_detail",
        "field": "tclprinciple",
        "logic": "加和计算",
        "note": "加和",
        "key": "prepayActualAmount"
    },
    "储值消费实收金额占比": {
        "table": "",
        "field": "会员使用储值的实收金额/(会员实收金额+会员使用储值的实收金额)",
        "logic": "会员使用储值的实收金额 / (会员实收金额 + 会员使用储值的实收金额) * 100",
        "note": "计算后除，以百分比显示",
        "key": "prepayConsumptionRatio"
    },
    "储值支付会员数": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_prepay_uv + overdue_uv",
        "logic": "正常储值人数 + 逾期储值人数",
        "note": "分别进行加和后进行加减法",
        "key": "prepayUsers"
    },
    "会员储值消费笔数": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_prepay_pv - cancel_prepay_pv + overdue_pv",
        "logic": "储值消费笔数 - 取消储值消费笔数 + 逾期消费笔数",
        "note": "分别进行加和后进行加减法",
        "key": "prepayConsumeCount"
    },
    "会员总实收金额": {
        "table": "dprpt_welife_consume_log",
        "field": "会员使用储值的实收金额+会员实收现金金额",
        "logic": "会员使用储值的实收金额 + 会员实收现金金额",
        "note": "分别进行加和后进行加减法",
        "key": "totalActualAmount"
    },
    "首次消费金额": {
        "table": "dprpt_welife_trade_consume_detail",
        "field": "SUM(CASE WHEN tcType = 2 THEN tcfee + tclprinciple ELSE 0 END) - SUM(CASE WHEN tcType = 3 THEN tcfee + tclprinciple ELSE 0 END)",
        "logic": "只消费过1次的会员的净销售额",
        "note": "通过子查询筛选首次消费会员",
        "key": "firstConsumeAmount"
    },
    "再次消费金额": {
        "table": "",
        "field": "会员总实收金额 - 首次消费金额",
        "logic": "会员总实收金额 - 首次消费金额",
        "note": "计算后减",
        "key": "repeatConsumeAmount"
    },
    "会员人均贡献": {
        "table": "dprpt_welife_consume_log",
        "field": "会员总实收金额/会员消费人数",
        "logic": "会员总实收金额/会员消费人数",
        "note": "计算后除",
        "key": "avgContribution"
    },
    "会员消费频次": {
        "table": "",
        "field": "会员总消费笔数/会员消费人数",
        "logic": "会员总消费笔数/会员消费人数",
        "note": "计算后除",
        "key": "consumeFrequency"
    },
    "会员单均消费": {
        "table": "",
        "field": "会员总实收金额/会员总消费笔数",
        "logic": "会员总实收金额/会员总消费笔数",
        "note": "计算后除",
        "key": "avgConsumeAmount"
    },
    "消费1次的会员数量": {
        "table": "dprpt_welife_trade_consume_detail",
        "field": "COUNT(DISTINCT uno) WHERE COUNT(*) = 1",
        "logic": "uno卡号出现1次的数量",
        "note": "按uno分组统计消费次数为1的会员",
        "key": "consumeOnceMembers"
    },
    "消费2次的会员数量": {
        "table": "dprpt_welife_trade_consume_detail",
        "field": "COUNT(DISTINCT uno) WHERE COUNT(*) = 2",
        "logic": "uno卡号出现2次的数量",
        "note": "按uno分组统计消费次数为2的会员",
        "key": "consumeTwiceMembers"
    },
    "消费3次的会员数量": {
        "table": "dprpt_welife_trade_consume_detail",
        "field": "COUNT(DISTINCT uno) WHERE COUNT(*) = 3",
        "logic": "uno卡号出现3次的数量",
        "note": "按uno分组统计消费次数为3的会员",
        "key": "consumeThriceMembers"
    },
    "消费3次以上会员数量": {
        "table": "dprpt_welife_trade_consume_detail",
        "field": "COUNT(DISTINCT uno) WHERE COUNT(*) > 3",
        "logic": "uno卡号出现3次以上的数量",
        "note": "按uno分组统计消费次数大于3的会员",
        "key": "consumeMoreThanThriceMembers"
    },
    "复购率": {
        "table": "",
        "field": "消费2次及以上的人数/会员消费人数",
        "logic": "(消费2次的会员数量 + 消费3次的会员数量 + 消费3次以上会员数量) / 会员消费人数 * 100",
        "note": "计算后除，以百分比显示",
        "key": "repurchaseRate"
    },
    "券带动总交易金额": {
        "table": "wedatas_welife_coupon_log",
        "field": "trade_amount - cancel_trade_amount",
        "logic": "交易总金额 - 撤销总金额",
        "note": "分别进行加和后进行加减法",
        "key": "couponTradeAmount"
    }
}

# 会员充值模块常量
MEMBER_CHARGE_MODULE = {
    "期间会员充值笔数": {
        "table": "dprpt_welife_charge_log_bid",
        "field": "charge_amount_pv - cancel_charge_amount_pv",
        "logic": "充值笔数 - 取消充值笔数",
        "note": "分别进行加和后进行加减法",
        "key": "chargeCount"
    },
    "期间充值实收总金额": {
        "table": "dprpt_welife_charge_log_bid",
        "field": "charge_cash - cancel_charge_cash",
        "logic": "实收金额 - 撤销储值实收金额",
        "note": "分别进行加和后进行加减法",
        "key": "chargeAmount"
    },
    "期间充值金额": {
        "table": "dprpt_welife_charge_log_bid",
        "field": "charge_amount - cancel_charge_amount",
        "logic": "充值金额 - 取消充值金额",
        "note": "分别进行加和后进行加减法",
        "key": "periodChargeAmount"
    },
    "期间充值赠送金额": {
        "table": "dprpt_welife_charge_log_bid",
        "field": "charge_present - cancel_charge_present",
        "logic": "充值赠送金额 - 取消充值赠送金额",
        "note": "分别进行加和后进行加减法",
        "key": "periodChargePresent"
    },
    "期末储值沉淀金额": {
        "table": "dprpt_welife_charge_log_bid",
        "field": "charge_amount_unused",
        "logic": "最后一天数据加和",
        "note": "取范围内最后一天数值加和",
        "key": "periodChargeAmountUnused"
    },
    "期间消耗储值实收总金额": {
        "table": "dprpt_welife_consume_log",
        "field": "consume_prepay - cancel_prepay + overdue_amount",
        "logic": "储值预存 - 取消储值预存 + 逾期金额",
        "note": "分别进行加和后进行加减法",
        "key": "consumePrepayAmount"
    },
    "存储留存率": {
        "table": "",
        "field": "(期间储值总金额-期间消费使用的储值金额)/期间储值总金额",
        "logic": "(期间储值总金额-期间消费使用的储值金额)/期间储值总金额",
        "note": "计算后除",
        "key": "retentionRate"
    }
}

# 券交易模块常量
COUPON_TRADE_MODULE = {
    "券名称": {
        "table": "wedatas_welife_coupon_log",
        "field": "couponname",
        "logic": "券名称",
        "note": "直接获取",
        "key": "couponName"
    },
    "券编号id": {
        "table": "wedatas_welife_coupon_log",
        "field": "couponid",
        "logic": "券id",
        "note": "直接获取",
        "key": "couponId"
    },
    "券发放量(张)": {
        "table": "wedatas_welife_coupon_log",
        "field": "coupon_send - cancel_coupon_send",
        "logic": "券发放数量 - 撤销券发放数量",
        "note": "分别进行加和后进行加减法",
        "key": "couponSendCount"
    },
    "券使用量(张)": {
        "table": "wedatas_welife_coupon_log",
        "field": "coupon_used - cancel_coupon_used",
        "logic": "券使用数量 - 撤销使用券数量",
        "note": "分别进行加和后进行加减法",
        "key": "couponUsedCount"
    },
    "券使用率(%)": {
        "table": "wedatas_welife_coupon_log",
        "field": "券使用量/券发放量",
        "logic": "券使用量/券发放量",
        "note": "计算后除",
        "key": "couponUsageRate"
    },
    "券抵扣金额(元)": {
        "table": "wedatas_welife_coupon_log",
        "field": "(coupon_used - cancel_coupon_used) * camount",
        "logic": "(券使用数量 - 撤销使用券数量) * 券抵扣金额",
        "note": "分别进行加和后进行乘法",
        "key": "couponDiscountAmount"
    },
    "带动储值消费(元)": {
        "table": "wedatas_welife_coupon_log",
        "field": "trade_prepay - cancel_trade_prepay",
        "logic": "交易预存 - 撤销预存",
        "note": "分别进行加和后进行加减法",
        "key": "drivePrepayAmount"
    },
    "带动现金消费(元)": {
        "table": "wedatas_welife_coupon_log",
        "field": "trade_cash - cancel_trade_cash",
        "logic": "交易现金 - 撤销现金",
        "note": "分别进行加和后进行加减法",
        "key": "driveCashAmount"
    },
    "带动总交易金额": {
        "table": "wedatas_welife_coupon_log",
        "field": "trade_amount - cancel_trade_amount",
        "logic": "交易总金额 - 撤销总金额",
        "note": "分别进行加和后进行加减法",
        "key": "driveTotalAmount"
    }
}

# 品智收银模块常量
PINZHI_CASHIER_MODULE = {
    "营业额总实收": {
        "table": "rept_ognperformance",
        "field": "total_actual_revenue",
        "logic": "所有订单的实际收入总和",
        "note": "包含现金、储值卡、第三方支付等所有实收金额",
        "key": "totalActualRevenue"
    },
    "营业额总应收": {
        "table": "rept_ognperformance",
        "field": "total_expected_revenue",
        "logic": "所有订单的应收金额总和",
        "note": "订单原价总金额，未扣除折扣优惠",
        "key": "totalExpectedRevenue"
    },
    "折扣率": {
        "table": "rept_ognperformance",
        "field": "total_actual_revenue / total_expected_revenue * 100",
        "logic": "营业额总实收 / 营业额总应收 * 100",
        "note": "计算后除，以百分比显示",
        "key": "discountRate"
    },
    "堂食(非外卖)营业额实收": {
        "table": "rept_ognperformance",
        "field": "dine_in_actual_revenue",
        "logic": "堂食订单的实际收入总和",
        "note": "仅包含非外卖订单的实收金额",
        "key": "dineInActualRevenue"
    },
    "堂食(非外卖)订单数": {
        "table": "rept_ognperformance",
        "field": "dine_in_order_count",
        "logic": "堂食订单数量统计",
        "note": "仅统计非外卖订单数量",
        "key": "dineInOrderCount"
    },
    "外卖营业额实收": {
        "table": "rept_ognperformance",
        "field": "takeout_actual_revenue",
        "logic": "外卖订单的实际收入总和",
        "note": "仅包含外卖订单的实收金额",
        "key": "takeoutActualRevenue"
    },
    "外卖订单数": {
        "table": "rept_ognperformance",
        "field": "takeout_order_count",
        "logic": "外卖订单数量统计",
        "note": "仅统计外卖订单数量",
        "key": "takeoutOrderCount"
    },
    "品牌平均客单价": {
        "table": "rept_ognperformance",
        "field": "brand_avg_order_value",
        "logic": "营业额总实收 ÷ (堂食订单数 + 外卖订单数)",
        "note": "衡量每单平均带来的营收",
        "key": "brandAvgOrderValue"
    },
    "堂食平均客单价": {
        "table": "rept_ognperformance",
        "field": "dine_in_avg_order_value",
        "logic": "堂食营业额实收 ÷ 堂食订单数",
        "note": "每个堂食订单的平均金额",
        "key": "dineInAvgOrderValue"
    },
    "外卖平均客单价": {
        "table": "rept_ognperformance",
        "field": "takeout_avg_order_value",
        "logic": "外卖营业额实收 ÷ 外卖订单数",
        "note": "每个外卖订单的平均金额",
        "key": "takeoutAvgOrderValue"
    },
    "堂食实收占比": {
        "table": "rept_ognperformance",
        "field": "dine_in_revenue_ratio",
        "logic": "堂食营业额实收 ÷ 营业额总实收",
        "note": "衡量堂食在整体营收中的比例",
        "key": "dineInRevenueRatio"
    },
    "外卖实收占比": {
        "table": "rept_ognperformance",
        "field": "takeout_revenue_ratio",
        "logic": "外卖营业额实收 ÷ 营业额总实收",
        "note": "衡量外卖在整体营收中的比例",
        "key": "takeoutRevenueRatio"
    },
    "订单总数": {
        "table": "rept_ognperformance",
        "field": "total_order_count",
        "logic": "堂食订单数 + 外卖订单数",
        "note": "总处理订单数量（可用于人效/坪效等）",
        "key": "totalOrderCount"
    },
    "会员总实收金额": {
        "table": "dprpt_welife_trade_consume_detail",
        "field": "member_total_actual_amount",
        "logic": "会员消费的总实收金额",
        "note": "来自会员消费模块的数据",
        "key": "memberTotalActualAmount"
    },
    "非会员总实收金额": {
        "table": "rept_ognperformance + dprpt_welife_trade_consume_detail",
        "field": "non_member_total_actual_amount",
        "logic": "品智营业额总实收 - 会员总实收金额",
        "note": "非会员消费的总实收金额",
        "key": "nonMemberTotalActualAmount"
    },
    "会员消费金额占堂食实收的比": {
        "table": "rept_ognperformance + dprpt_welife_trade_consume_detail",
        "field": "member_dine_in_ratio",
        "logic": "会员总实收金额 ÷ 堂食营业额实收",
        "note": "会员消费在堂食中的占比",
        "key": "memberDineInRatio"
    },
    "会员总消费笔数": {
        "table": "dprpt_welife_trade_consume_detail",
        "field": "member_total_consume_count",
        "logic": "会员消费的总笔数",
        "note": "来自会员消费模块的数据",
        "key": "memberTotalConsumeCount"
    },
    "会员消费单数占堂食单数的比": {
        "table": "rept_ognperformance + dprpt_welife_trade_consume_detail",
        "field": "member_dine_in_count_ratio",
        "logic": "会员总消费笔数 ÷ 堂食订单数",
        "note": "会员消费笔数在堂食订单中的占比",
        "key": "memberDineInCountRatio"
    }
}

# 所有模块的统一索引
ALL_MODULES = {
    "会员基础模块": MEMBER_BASE_MODULE,
    "会员消费模块": MEMBER_CONSUME_MODULE,
    "会员充值模块": MEMBER_CHARGE_MODULE,
    "券交易模块": COUPON_TRADE_MODULE,
    "品智收银模块": PINZHI_CASHIER_MODULE
}