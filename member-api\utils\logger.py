import logging
import sys
from typing import Optional

# 创建全局logger实例
logger = logging.getLogger('app')

def setup_logging(log_level: Optional[str] = None):
    """配置全局日志格式"""
    
    # 设置日志级别
    level = getattr(logging, log_level.upper()) if log_level else logging.INFO
    
    # 清除所有已存在的处理器
    logging.root.handlers = []
    logger.handlers = []
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s [%(levelname)s] %(message)s',
        '%Y-%m-%d %H:%M:%S'
    )
    
    # 创建处理器
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(formatter)
    
    # 配置root logger
    logging.root.addHandler(handler)
    logging.root.setLevel(level)
    
    # 配置app logger
    logger.addHandler(handler)
    logger.setLevel(level)
    logger.propagate = False
    
    # 禁用其他logger
    for name in logging.root.manager.loggerDict:
        if name != 'app':
            logging.getLogger(name).handlers = []
            logging.getLogger(name).propagate = False
            logging.getLogger(name).addHandler(logging.NullHandler())