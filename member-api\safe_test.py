#!/usr/bin/env python3
"""
安全测试脚本 - 首次消费金额查询优化（分批处理版本）

使用分批处理避免对MySQL造成压力
"""

import asyncio
import logging
import math

# 导入数据库连接
from core.database import Database

# 配置日志 - 记录实际发生时间
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

async def safe_optimized_query():
    """安全的优化查询方案 - 使用分批处理"""
    
    # 测试参数
    bid = "1703248138"
    start_date = "20250601"
    end_date = "20250630"
    batch_size = 1000  # 每批处理1000个tcid，避免IN子句过长
    
    logger.info("🛡️ 开始安全的首次消费查询测试")
    logger.info(f"📋 测试条件: bid={bid}, 时间范围={start_date}-{end_date}")
    logger.info(f"🔧 批处理大小: {batch_size}")
    logger.info("⏰ 开始记录实际执行时间")
    
    # 初始化数据库连接
    db_manager = Database()
    await db_manager.connect()
    
    try:
        # 第一步：找出每个会员的全局首次消费记录ID（不限时间）
        first_tcid_sql = f"""
        SELECT uno, MIN(tcid) AS first_tcid
        FROM dwoutput.dprpt_welife_trade_consume_detail
        WHERE bid = {bid}
        GROUP BY uno
        """
        
        logger.info("🚀 第一步开始：查询会员首次消费记录")
        first_tcid_records = await db_manager.execute_dwoutput_query(first_tcid_sql)
        logger.info(f"✅ 第一步完成，获得 {len(first_tcid_records)} 个会员的首次消费记录")
        
        if not first_tcid_records:
            logger.warning("⚠️  没有找到任何会员的首次消费记录")
            return
        
        # 准备分批处理
        first_tcids = [str(record['first_tcid']) for record in first_tcid_records]
        total_tcids = len(first_tcids)
        batch_count = math.ceil(total_tcids / batch_size)
        
        logger.info(f"🔧 准备分批处理: 总计{total_tcids}条记录，分{batch_count}批，每批{batch_size}条")
        
        # 第二步：分批查询首次消费记录详情
        logger.info("🚀 第二步开始：分批查询首次消费记录详情")
        all_records = []
        
        for batch_idx in range(batch_count):
            start_idx = batch_idx * batch_size
            end_idx = min((batch_idx + 1) * batch_size, total_tcids)
            batch_tcids = first_tcids[start_idx:end_idx]
            tcid_list = ','.join(batch_tcids)
            
            logger.info(f"📊 第二步 - 批次{batch_idx + 1}/{batch_count}: 处理{len(batch_tcids)}条记录...")

            detail_sql = f"""
            SELECT
                uno,
                tcid,
                tcfee,
                tcstoredpay,
                tclprinciple,
                tcType,
                ftime
            FROM dwoutput.dprpt_welife_trade_consume_detail
            WHERE bid = {bid}
              AND tcid IN ({tcid_list})
              AND ftime BETWEEN {start_date} AND {end_date}
            """
            
            batch_records = await db_manager.execute_dwoutput_query(detail_sql)
            all_records.extend(batch_records)
            logger.info(f"✅ 批次{batch_idx + 1}完成: {len(batch_records)}条记录")
            
            # 添加小延迟，避免对数据库造成过大压力
            if batch_idx < batch_count - 1:  # 最后一批不需要延迟
                await asyncio.sleep(0.1)  # 100ms延迟
        
        logger.info(f"✅ 第二步全部完成，获得 {len(all_records)} 条在时间范围内的首次消费记录")
        
        # 显示前几条记录样例
        logger.info("📋 前3条记录样例:")
        for i, record in enumerate(all_records[:3]):
            logger.info(f"  记录{i+1}: 会员={record['uno']}, "
                       f"类型={record['tcType']}, 金额={record['tcfee']}, "
                       f"储值={record['tcstoredpay']}, 时间={record['ftime']}")
        
        # 应用层计算首次消费金额
        logger.info("🧮 开始应用层计算首次消费金额...")
        
        total_first_consume_amount = 0.0
        type2_count = 0
        type3_count = 0
        
        for record in all_records:
            tcType = record['tcType']
            
            if tcType == 2:  # 正常消费
                amount = (record['tcfee'] or 0) + (record['tcstoredpay'] or 0)
                total_first_consume_amount += amount
                type2_count += 1
            elif tcType == 3:  # 取消消费
                amount = (record['tcfee'] or 0) + (record['tclprinciple'] or 0)
                total_first_consume_amount -= amount
                type3_count += 1
        
        logger.info("✅ 应用层计算完成")

        # 输出最终结果
        logger.info("="*60)
        logger.info("📊 安全测试结果汇总")
        logger.info("="*60)
        logger.info(f"📊 处理批次数: {batch_count}批")
        logger.info(f"📊 首次消费记录数: {len(all_records)}")
        logger.info(f"📊 正常消费记录数: {type2_count}")
        logger.info(f"📊 取消消费记录数: {type3_count}")
        logger.info(f"💰 首次消费金额: {total_first_consume_amount:.2f}")
        logger.info("="*60)
        
        # 性能评估
        logger.info("🛡️ 性能评估:")
        logger.info(f"📊 批处理大小: {batch_size}")
        logger.info(f"📊 批次数量: {batch_count}")

        if batch_count > 10:
            logger.warning("⚠️  批次数量较多，建议增加batch_size或减少测试范围")
        else:
            logger.info("✅ 批次数量合理，对数据库压力可控")
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        
    finally:
        # 注意：Database类没有close方法，连接池会自动管理
        logger.info("🏁 安全测试完成")

if __name__ == "__main__":
    asyncio.run(safe_optimized_query())
