# -*- coding: utf-8 -*-
"""
AI数据分析提示词模板
针对会员数据查询系统的不同模块提供专业的数据分析模板
"""

from typing import Dict, Any, List, Union
import json
import random

class QueryAnalysisPrompts:
    """查询分析提示词类"""

    # 会员基础数据分析模板句式
    MEMBER_BASE_ANALYSIS_TEMPLATES = [
        "会员总量{total_members}人，新增会员{new_members}人，增长率{growth_rate:.1f}%，{growth_assessment}。",
        "会员信息完善度：手机号完善率{phone_complete_ratio:.1f}%，基础信息完善率{info_complete_ratio:.1f}%，{completion_assessment}。",
        "会员活跃度表现：消费会员占比{consume_ratio:.1f}%，充值会员占比{charge_ratio:.1f}%，{activity_assessment}。",
        "会员净增量{net_increase}人，流失率{churn_rate:.1f}%，{retention_assessment}。",
        "会员结构分析：新会员质量{new_member_quality}，老会员活跃度{old_member_activity}，{structure_assessment}。",
        "会员获取成本{acquisition_cost}，生命周期价值{lifetime_value}，投入产出比{roi_assessment}。",
        "会员分层情况：高价值会员{high_value_members}人，潜力会员{potential_members}人，{segmentation_assessment}。",
        "会员增长趋势{growth_trend}，季节性特征{seasonal_pattern}，{trend_assessment}。",
        "会员转化漏斗：注册转化率{registration_rate:.1f}%，激活转化率{activation_rate:.1f}%，{funnel_assessment}。",
        "会员忠诚度指标：复购率{repurchase_rate:.1f}%，推荐率{referral_rate:.1f}%，{loyalty_assessment}。"
    ]

    # 会员消费数据分析模板句式
    MEMBER_CONSUME_ANALYSIS_TEMPLATES = [
        "消费总额{total_amount}元，消费人数{consume_users}人，人均消费{avg_contribution:.2f}元，{consumption_assessment}。",
        "消费结构：储值消费{prepay_amount}元（{prepay_ratio:.1f}%），现金消费{cash_amount}元（{cash_ratio:.1f}%），{structure_assessment}。",
        "消费频次{consume_frequency:.2f}次/人，单均消费{avg_consume_amount:.2f}元/笔，{frequency_assessment}。",
        "消费转化：消费会员占比{consume_ratio:.1f}%，重复消费率{repeat_rate:.1f}%，{conversion_assessment}。",
        "消费趋势：环比增长{mom_growth:.1f}%，同比增长{yoy_growth:.1f}%，{trend_assessment}。",
        "消费分层：高消费会员{high_spenders}人，中等消费{medium_spenders}人，{segmentation_assessment}。",
        "消费时段分布：工作日消费{weekday_ratio:.1f}%，周末消费{weekend_ratio:.1f}%，{timing_assessment}。",
        "消费品类偏好：主力品类{main_category}，增长品类{growth_category}，{preference_assessment}。",
        "消费客单价{avg_order_value:.2f}元，消费笔数{total_orders}笔，{efficiency_assessment}。",
        "消费留存：首月留存{first_month_retention:.1f}%，三月留存{third_month_retention:.1f}%，{retention_assessment}。"
    ]

    # 会员充值数据分析模板句式
    MEMBER_CHARGE_ANALYSIS_TEMPLATES = [
        "充值总额{total_charge_amount}元，充值人数{charge_users}人，人均充值{avg_charge_amount:.2f}元，{charge_assessment}。",
        "充值转化：充值会员占比{charge_ratio:.1f}%，充值转化率{charge_conversion:.1f}%，{conversion_assessment}。",
        "充值档位分布：小额充值{small_charge_ratio:.1f}%，大额充值{large_charge_ratio:.1f}%，{tier_assessment}。",
        "储值使用率{usage_rate:.1f}%，储值余额{balance_amount}元，{usage_assessment}。",
        "充值频次{charge_frequency:.2f}次/人，充值周期{charge_cycle}天，{frequency_assessment}。",
        "充值活动效果：活动期充值{promotion_charge}元，平时充值{normal_charge}元，{promotion_assessment}。",
        "充值留存：充值后消费率{post_charge_consume:.1f}%，储值消耗周期{consumption_cycle}天，{retention_assessment}。",
        "充值趋势：环比增长{mom_growth:.1f}%，同比增长{yoy_growth:.1f}%，{trend_assessment}。",
        "充值用户分层：高频充值{frequent_chargers}人，低频充值{occasional_chargers}人，{segmentation_assessment}。",
        "充值ROI：充值带动消费{driven_consumption}元，投入产出比{charge_roi:.2f}，{roi_assessment}。"
    ]

    # 券交易数据分析模板句式
    COUPON_TRADE_ANALYSIS_TEMPLATES = [
        "券发放总量{total_coupons}张，使用量{used_coupons}张，使用率{usage_rate:.1f}%，{usage_assessment}。",
        "券带动消费{driven_amount}元，券成本{coupon_cost}元，投入产出比{roi:.2f}，{roi_assessment}。",
        "券类型分布：满减券{discount_coupons}张，折扣券{percentage_coupons}张，{type_assessment}。",
        "券使用转化：领取转化率{claim_rate:.1f}%，使用转化率{usage_conversion:.1f}%，{conversion_assessment}。",
        "券用户覆盖：覆盖用户{covered_users}人，人均领取{avg_claim:.2f}张，{coverage_assessment}。",
        "券效果分析：新客获取{new_customer_acquisition}人，老客激活{old_customer_activation}人，{effect_assessment}。",
        "券使用时间：即时使用{immediate_usage:.1f}%，延迟使用{delayed_usage:.1f}%，{timing_assessment}。",
        "券面值分布：低面值{low_value_ratio:.1f}%，高面值{high_value_ratio:.1f}%，{value_assessment}。",
        "券活动效果：活动期使用{promotion_usage}张，平时使用{normal_usage}张，{activity_assessment}。",
        "券用户行为：重复使用率{repeat_usage:.1f}%，推荐分享率{share_rate:.1f}%，{behavior_assessment}。"
    ]

    # 品智收银数据分析模板句式
    PINZHI_CASHIER_ANALYSIS_TEMPLATES = [
        "营业额总实收{total_actual_revenue}元，总应收{total_expected_revenue}元，实收率{actual_rate:.1f}%，{revenue_assessment}。",
        "折扣率{discount_rate:.1f}%，环比变化{discount_mom_change}，同比变化{discount_yoy_change}，{discount_assessment}。",
        "堂食营业额{dine_in_revenue}元，占比{dine_in_ratio:.1f}%，外卖营业额{takeout_revenue}元，占比{takeout_ratio:.1f}%，{channel_assessment}。",
        "堂食订单{dine_in_orders}单，客单价{dine_in_avg:.2f}元，外卖订单{takeout_orders}单，客单价{takeout_avg:.2f}元，{efficiency_assessment}。",
        "营业额环比增长{revenue_mom_growth:.1f}%，同比增长{revenue_yoy_growth:.1f}%，{growth_assessment}。",
        "订单总量{total_orders}单，环比变化{orders_mom_change}，同比变化{orders_yoy_change}，{volume_assessment}。",
        "堂食外卖结构{channel_structure}，渠道均衡度{channel_balance}，{structure_assessment}。",
        "收银效率{cashier_efficiency}，订单处理{order_processing}，{operational_assessment}。",
        "业绩波动{performance_volatility}，增长稳定性{growth_stability}，{stability_assessment}。",
        "盈利能力{profitability}，成本控制{cost_control}，{profitability_assessment}。"
    ]

def get_analysis_prompt(module_type: str, data: Union[Dict[str, Any], List[Dict[str, Any]]], query_params: Dict[str, Any]) -> str:
    """
    获取分析提示词

    Args:
        module_type: 模块类型 (member_base, member_consume, member_charge, coupon_trade, pinzhi_cashier)
        data: 数据
        query_params: 查询参数

    Returns:
        str: 提示词
    """
    if module_type == "member_base":
        return get_member_base_prompt(data, query_params)
    elif module_type == "member_consume":
        return get_member_consume_prompt(data, query_params)
    elif module_type == "member_charge":
        return get_member_charge_prompt(data, query_params)
    elif module_type == "coupon_trade":
        return get_coupon_trade_prompt(data, query_params)
    elif module_type == "pinzhi_cashier":
        return get_pinzhi_cashier_prompt(data, query_params)
    else:
        raise ValueError(f"不支持的模块类型: {module_type}")

def get_member_base_prompt(data: Union[Dict[str, Any], object], query_params: Dict[str, Any]) -> str:
    """会员基础数据分析提示词"""
    # 如果data是Pydantic模型对象，先转换为字典
    if hasattr(data, 'model_dump'):
        data_dict = data.model_dump()
    elif hasattr(data, 'dict'):
        data_dict = data.dict()
    else:
        data_dict = data

    # 获取时间范围信息
    time_frame = f"{query_params.get('start_date', '未知')} 至 {query_params.get('end_date', '未知')}"

    # 获取会员基础分析模板句式
    member_templates = QueryAnalysisPrompts.MEMBER_BASE_ANALYSIS_TEMPLATES

    return f"""
你是一位专业的会员运营数据分析师，请基于以下会员基础数据进行深度分析，从提供的模板句式中选择最合适的进行分析。

## 数据统计时间范围
**分析期间：{time_frame}**

## 查询条件详情
- 查询类型: {query_params.get('query_type', '未知')}
- 品牌ID: {query_params.get('bid', '未知')}
- 门店ID: {query_params.get('sid', '全部门店' if not query_params.get('sid') else query_params.get('sid'))}

## 会员基础数据详情
{json.dumps(data_dict, ensure_ascii=False, indent=2)}

## 可选分析模板句式
{chr(10).join([f"{i+1}. {template}" for i, template in enumerate(member_templates)])}

## 分析任务
基于上述会员基础数据，识别关键问题并提供具体的改进建议。重点关注数据发现、问题诊断和详细解决方案。

## 输出要求
请严格按照以下格式输出5条分析，每条分析独占一行：

1、[数据发现 + 存在问题 + 详细解决思路和运营建议]
2、[数据发现 + 存在问题 + 详细解决思路和运营建议]
3、[数据发现 + 存在问题 + 详细解决思路和运营建议]
4、[数据发现 + 存在问题 + 详细解决思路和运营建议]
5、[数据发现 + 存在问题 + 详细解决思路和运营建议]

分析重点：
- 数据发现：基于实际数据的客观发现和趋势识别
- 问题识别：明确指出数据反映的具体问题和风险点
- 解决思路：提供详细的解决方案和具体的运营策略
- 每条分析控制在120字以内，确保内容充实且具有可操作性
- 必须基于{time_frame}期间的实际数据进行分析
- 分析结论应体现时间范围的业务意义和可执行的改进措施
"""


def get_member_consume_prompt(data: Union[List[Dict[str, Any]], object], query_params: Dict[str, Any]) -> str:
    """会员消费数据分析提示词"""
    # 如果data是Pydantic模型对象，先转换为字典
    if hasattr(data, 'model_dump'):
        data_dict = data.model_dump()
    elif hasattr(data, 'dict'):
        data_dict = data.dict()
    else:
        data_dict = data

    # 获取时间范围信息
    time_frame = f"{query_params.get('start_date', '未知')} 至 {query_params.get('end_date', '未知')}"

    # 获取会员消费分析模板句式
    consume_templates = QueryAnalysisPrompts.MEMBER_CONSUME_ANALYSIS_TEMPLATES

    return f"""
你是一位专业的会员消费数据分析师，请基于以下会员消费数据进行深度分析，从提供的模板句式中选择最合适的进行分析。

## 数据统计时间范围
**分析期间：{time_frame}**

## 查询条件详情
- 查询类型: {query_params.get('query_type', '未知')}
- 品牌ID: {query_params.get('bid', '未知')}
- 门店ID: {query_params.get('sid', '全部门店' if not query_params.get('sid') else query_params.get('sid'))}

## 会员消费数据详情
{json.dumps(data_dict, ensure_ascii=False, indent=2)}

## 可选分析模板句式
{chr(10).join([f"{i+1}. {template}" for i, template in enumerate(consume_templates)])}

## 分析任务
基于上述会员消费数据，识别关键问题并提供具体的改进建议。重点关注消费结构、消费行为和收入优化。

## 输出要求
请严格按照以下格式输出5条分析，每条分析独占一行：

1、[数据发现 + 存在问题 + 详细解决思路和运营建议]
2、[数据发现 + 存在问题 + 详细解决思路和运营建议]
3、[数据发现 + 存在问题 + 详细解决思路和运营建议]
4、[数据发现 + 存在问题 + 详细解决思路和运营建议]
5、[数据发现 + 存在问题 + 详细解决思路和运营建议]

分析重点：
- 数据发现：基于消费数据的客观发现和消费趋势分析
- 问题识别：明确指出消费结构和行为中的具体问题
- 解决思路：提供详细的消费优化方案和收入提升策略
- 每条分析控制在120字以内，确保策略具体且可操作
- 必须基于{time_frame}期间的实际消费数据进行分析
- 分析结论应体现消费行为洞察和可执行的收入优化措施
"""


def get_member_charge_prompt(data: Union[List[Dict[str, Any]], object], query_params: Dict[str, Any]) -> str:
    """会员充值数据分析提示词"""
    # 如果data是Pydantic模型对象，先转换为字典
    if hasattr(data, 'model_dump'):
        data_dict = data.model_dump()
    elif hasattr(data, 'dict'):
        data_dict = data.dict()
    else:
        data_dict = data

    # 获取时间范围信息
    time_frame = f"{query_params.get('start_date', '未知')} 至 {query_params.get('end_date', '未知')}"

    # 获取会员充值分析模板句式
    charge_templates = QueryAnalysisPrompts.MEMBER_CHARGE_ANALYSIS_TEMPLATES

    return f"""
你是一位专业的会员充值数据分析师，请基于以下会员充值数据进行深度分析，从提供的模板句式中选择最合适的进行分析。

## 数据统计时间范围
**分析期间：{time_frame}**

## 查询条件详情
- 查询类型: {query_params.get('query_type', '未知')}
- 品牌ID: {query_params.get('bid', '未知')}
- 门店ID: {query_params.get('sid', '全部门店' if not query_params.get('sid') else query_params.get('sid'))}

## 会员充值数据详情
{json.dumps(data_dict, ensure_ascii=False, indent=2)}

## 可选分析模板句式
{chr(10).join([f"{i+1}. {template}" for i, template in enumerate(charge_templates)])}

## 分析任务
基于上述会员充值数据，识别关键问题并提供具体的改进建议。重点关注充值转化、储值管理和资金流优化。

## 输出要求
请严格按照以下格式输出5条分析，每条分析独占一行：

1、[数据发现 + 存在问题 + 详细解决思路和运营建议]
2、[数据发现 + 存在问题 + 详细解决思路和运营建议]
3、[数据发现 + 存在问题 + 详细解决思路和运营建议]
4、[数据发现 + 存在问题 + 详细解决思路和运营建议]
5、[数据发现 + 存在问题 + 详细解决思路和运营建议]

分析重点：
- 数据发现：基于充值数据的客观发现和资金流分析
- 问题识别：明确指出充值转化和储值管理中的具体问题
- 解决思路：提供详细的充值优化方案和储值运营策略
- 每条分析控制在120字以内，确保策略具体且可操作
- 必须基于{time_frame}期间的实际充值数据进行分析
- 分析结论应体现充值行为洞察和可执行的资金流优化措施
"""


def get_coupon_trade_prompt(data: Union[List[Dict[str, Any]], object], query_params: Dict[str, Any]) -> str:
    """券交易数据分析提示词"""
    # 如果data是Pydantic模型对象，先转换为字典
    if hasattr(data, 'model_dump'):
        data_dict = data.model_dump()
    elif hasattr(data, 'dict'):
        data_dict = data.dict()
    else:
        data_dict = data

    # 获取时间范围信息
    time_frame = f"{query_params.get('start_date', '未知')} 至 {query_params.get('end_date', '未知')}"

    # 获取券交易分析模板句式
    coupon_templates = QueryAnalysisPrompts.COUPON_TRADE_ANALYSIS_TEMPLATES

    return f"""
你是一位专业的券交易数据分析师，请基于以下券交易数据进行深度分析，从提供的模板句式中选择最合适的进行分析。

## 数据统计时间范围
**分析期间：{time_frame}**

## 查询条件详情
- 查询类型: {query_params.get('query_type', '未知')}
- 品牌ID: {query_params.get('bid', '未知')}
- 门店ID: {query_params.get('sid', '全部门店' if not query_params.get('sid') else query_params.get('sid'))}

## 券交易数据详情
{json.dumps(data_dict, ensure_ascii=False, indent=2)}

## 可选分析模板句式
{chr(10).join([f"{i+1}. {template}" for i, template in enumerate(coupon_templates)])}

## 分析任务
基于上述券交易数据，识别关键问题并提供具体的改进建议。重点关注券效果、成本控制和用户转化。

## 输出要求
请严格按照以下格式输出5条分析，每条分析独占一行：

1、[数据发现 + 存在问题 + 详细解决思路和运营建议]
2、[数据发现 + 存在问题 + 详细解决思路和运营建议]
3、[数据发现 + 存在问题 + 详细解决思路和运营建议]
4、[数据发现 + 存在问题 + 详细解决思路和运营建议]
5、[数据发现 + 存在问题 + 详细解决思路和运营建议]

分析重点：
- 数据发现：基于券交易数据的客观发现和效果分析
- 问题识别：明确指出券策略和运营中的具体问题
- 解决思路：提供详细的券优化方案和成本控制策略
- 每条分析控制在120字以内，确保策略具体且可操作
- 必须基于{time_frame}期间的实际券交易数据进行分析
- 分析结论应体现券营销洞察和可执行的效果优化措施
"""


def get_pinzhi_cashier_prompt(data: Union[Dict[str, Any], object], query_params: Dict[str, Any]) -> str:
    """品智收银数据分析提示词"""
    # 如果data是Pydantic模型对象，先转换为字典
    if hasattr(data, 'model_dump'):
        data_dict = data.model_dump()
    elif hasattr(data, 'dict'):
        data_dict = data.dict()
    else:
        data_dict = data

    # 获取时间范围信息
    time_frame = f"{query_params.get('start_date', '未知')} 至 {query_params.get('end_date', '未知')}"
    merchant_id = query_params.get('merchant_id', '未知商户')

    # 获取品智收银分析模板句式
    pinzhi_templates = QueryAnalysisPrompts.PINZHI_CASHIER_ANALYSIS_TEMPLATES

    return f"""
你是一位专业的餐饮收银数据分析师，请基于以下品智收银数据进行深度分析，从提供的模板句式中选择最合适的进行分析。

## 查询条件详情
- 查询类型: {query_params.get('query_type', '未知')}
- 商户: {merchant_id}
- 时间范围: {time_frame}

## 品智收银数据详情
{json.dumps(data_dict, ensure_ascii=False, indent=2)}

## 可选分析模板句式
{chr(10).join([f"{i+1}. {template}" for i, template in enumerate(pinzhi_templates)])}

## 分析任务
基于上述品智收银数据，识别关键问题并提供具体的改进建议。重点关注营业额表现、渠道结构、运营效率和增长趋势。

## 输出要求
请严格按照以下格式输出5条分析，每条分析独占一行：

1、[数据发现 + 存在问题 + 详细解决思路和运营建议]
2、[数据发现 + 存在问题 + 详细解决思路和运营建议]
3、[数据发现 + 存在问题 + 详细解决思路和运营建议]
4、[数据发现 + 存在问题 + 详细解决思路和运营建议]
5、[数据发现 + 存在问题 + 详细解决思路和运营建议]

分析重点：
- 数据发现：基于收银数据的客观发现和业绩趋势分析
- 问题识别：明确指出营业额、渠道结构和运营中的具体问题
- 解决思路：提供详细的收银优化方案和业绩提升策略
- 每条分析控制在120字以内，确保策略具体且可操作
- 必须基于{time_frame}期间的实际收银数据进行分析
- 分析结论应体现收银业务洞察和可执行的运营优化措施
"""
