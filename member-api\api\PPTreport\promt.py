# -*- coding: utf-8 -*-
"""
AI数据分析提示词模板
针对PPT报告的不同页面提供专业的数据分析模板
"""

from typing import Dict, Any
import random

class AIAnalysisPrompts:
    """AI分析提示词类"""

    # 会员数据分析报告模板句式（12-15个）
    MEMBER_DATA_ANALYSIS_TEMPLATES = [
        "根据数据显示，会员总量达到{total_members}人，其中新增会员{new_members}人，占比{new_member_ratio:.1f}%，表明{growth_trend}。",
        "会员基础信息完善度方面，{complete_phone_members}人完善了手机号（{phone_complete_ratio:.1f}%），{total_complete_members}人完善了基础资料（{info_complete_ratio:.1f}%），{completion_assessment}。",
        "从会员活跃度来看，{total_consume_members}人产生了消费行为（{consume_ratio:.1f}%），{total_charge_members}人进行了充值（{charge_ratio:.1f}%），{activity_assessment}。",
        "会员增长趋势显示{growth_analysis}，建议{growth_suggestion}。",
        "会员质量分析表明{quality_analysis}，{quality_suggestion}。",
        "新增会员占比{new_member_ratio:.1f}%，{new_member_analysis}，{retention_suggestion}。",
        "会员信息完善率{info_complete_ratio:.1f}%，{info_complete_analysis}，{info_improvement_suggestion}。",
        "会员消费参与度{consume_ratio:.1f}%，{consume_analysis}，{consume_improvement_suggestion}。",
        "会员充值参与度{charge_ratio:.1f}%，{charge_analysis}，{charge_strategy_suggestion}。",
        "整体会员结构{structure_analysis}，{structure_optimization_suggestion}。",
        "会员留存情况{retention_analysis}，{retention_strategy_suggestion}。",
        "会员价值分层显示{value_analysis}，{value_enhancement_suggestion}。",
        "会员运营效果{operation_analysis}，{operation_optimization_suggestion}。",
        "会员发展潜力{potential_analysis}，{potential_development_suggestion}。",
        "会员管理现状{management_analysis}，{management_improvement_suggestion}。"
    ]

    # 会员收入分析报告模板句式（12-15个）
    MEMBER_REVENUE_ANALYSIS_TEMPLATES = [
        "会员总消费金额达到{total_actual_amount}元，平均消费金额{avg_consume_amount}元，消费频次{consume_frequency}次，{revenue_trend_analysis}。",
        "储值消费占比{prepay_ratio:.1f}%（{prepay_actual_amount}元），现金消费占比{cash_ratio:.1f}%，{payment_structure_analysis}。",
        "首次消费金额{first_consume_amount}元，重复消费金额{repeat_consume_amount}元，重复消费占比{repeat_ratio:.1f}%，{loyalty_analysis}。",
        "消费会员数{consume_users}人，人均消费{per_capita_consumption:.2f}元，{consumption_efficiency_analysis}。",
        "收入结构分析显示{revenue_structure_analysis}，{revenue_optimization_suggestion}。",
        "消费频次{consume_frequency}次表明{frequency_analysis}，{frequency_improvement_suggestion}。",
        "平均消费金额{avg_consume_amount}元{amount_analysis}，{amount_enhancement_suggestion}。",
        "储值使用情况{prepay_usage_analysis}，{prepay_strategy_suggestion}。",
        "重复消费表现{repeat_consumption_analysis}，{repeat_consumption_strategy}。",
        "消费转化率{consumption_conversion_analysis}，{conversion_improvement_suggestion}。",
        "收入增长潜力{revenue_growth_analysis}，{growth_strategy_suggestion}。",
        "消费行为特征{consumption_behavior_analysis}，{behavior_optimization_suggestion}。",
        "收入质量评估{revenue_quality_analysis}，{quality_improvement_suggestion}。",
        "消费价值分布{consumption_value_analysis}，{value_maximization_suggestion}。",
        "收入稳定性{revenue_stability_analysis}，{stability_enhancement_suggestion}。"
    ]

    @classmethod
    def generate_member_data_analysis(cls, data: Dict[str, Any]) -> str:
        """
        生成会员数据分析文本

        Args:
            data: 会员数据字典

        Returns:
            str: 分析文本
        """
        try:
            # 提取数据
            total_members = int(data.get("total_members", 0))
            new_members = int(data.get("new_members", 0))
            complete_phone_members = int(data.get("complete_phone_members", 0))
            total_complete_members = int(data.get("total_complete_members", 0))
            total_consume_members = int(data.get("total_consume_members", 0))
            total_charge_members = int(data.get("total_charge_members", 0))

            # 计算比例
            new_member_ratio = (new_members / total_members * 100) if total_members > 0 else 0
            phone_complete_ratio = (complete_phone_members / total_members * 100) if total_members > 0 else 0
            info_complete_ratio = (total_complete_members / total_members * 100) if total_members > 0 else 0
            consume_ratio = (total_consume_members / total_members * 100) if total_members > 0 else 0
            charge_ratio = (total_charge_members / total_members * 100) if total_members > 0 else 0

            # 生成分析内容
            analysis_parts = []

            # 选择3-4个模板进行分析
            selected_templates = random.sample(cls.MEMBER_DATA_ANALYSIS_TEMPLATES, min(4, len(cls.MEMBER_DATA_ANALYSIS_TEMPLATES)))

            for template in selected_templates:
                try:
                    analysis_text = template.format(
                        total_members=total_members,
                        new_members=new_members,
                        new_member_ratio=new_member_ratio,
                        complete_phone_members=complete_phone_members,
                        phone_complete_ratio=phone_complete_ratio,
                        total_complete_members=total_complete_members,
                        info_complete_ratio=info_complete_ratio,
                        total_consume_members=total_consume_members,
                        consume_ratio=consume_ratio,
                        total_charge_members=total_charge_members,
                        charge_ratio=charge_ratio,
                        growth_trend=cls._get_growth_trend(new_member_ratio),
                        completion_assessment=cls._get_completion_assessment(info_complete_ratio),
                        activity_assessment=cls._get_activity_assessment(consume_ratio),
                        growth_analysis=cls._get_growth_analysis(new_member_ratio),
                        growth_suggestion=cls._get_growth_suggestion(new_member_ratio),
                        quality_analysis=cls._get_quality_analysis(info_complete_ratio, consume_ratio),
                        quality_suggestion=cls._get_quality_suggestion(info_complete_ratio),
                        new_member_analysis=cls._get_new_member_analysis(new_member_ratio),
                        retention_suggestion=cls._get_retention_suggestion(new_member_ratio),
                        info_complete_analysis=cls._get_info_complete_analysis(info_complete_ratio),
                        info_improvement_suggestion=cls._get_info_improvement_suggestion(info_complete_ratio),
                        consume_analysis=cls._get_consume_analysis(consume_ratio),
                        consume_improvement_suggestion=cls._get_consume_improvement_suggestion(consume_ratio),
                        charge_analysis=cls._get_charge_analysis(charge_ratio),
                        charge_strategy_suggestion=cls._get_charge_strategy_suggestion(charge_ratio),
                        structure_analysis=cls._get_structure_analysis(new_member_ratio, consume_ratio),
                        structure_optimization_suggestion=cls._get_structure_optimization_suggestion(consume_ratio),
                        retention_analysis=cls._get_retention_analysis(new_member_ratio),
                        retention_strategy_suggestion=cls._get_retention_strategy_suggestion(new_member_ratio),
                        value_analysis=cls._get_value_analysis(consume_ratio, charge_ratio),
                        value_enhancement_suggestion=cls._get_value_enhancement_suggestion(consume_ratio),
                        operation_analysis=cls._get_operation_analysis(info_complete_ratio, consume_ratio),
                        operation_optimization_suggestion=cls._get_operation_optimization_suggestion(consume_ratio),
                        potential_analysis=cls._get_potential_analysis(new_member_ratio, consume_ratio),
                        potential_development_suggestion=cls._get_potential_development_suggestion(new_member_ratio),
                        management_analysis=cls._get_management_analysis(info_complete_ratio),
                        management_improvement_suggestion=cls._get_management_improvement_suggestion(info_complete_ratio)
                    )
                    analysis_parts.append(analysis_text)
                except (KeyError, ValueError) as e:
                    # 如果模板格式化失败，跳过这个模板
                    continue

            return " ".join(analysis_parts) if analysis_parts else "数据分析暂时无法生成，请检查数据完整性。"

        except Exception as e:
            return f"数据分析生成失败: {str(e)}"

    # 辅助方法 - 会员数据分析
    @classmethod
    def _get_growth_trend(cls, ratio: float) -> str:
        if ratio >= 20:
            return "会员增长势头强劲"
        elif ratio >= 10:
            return "会员增长稳定"
        elif ratio >= 5:
            return "会员增长缓慢"
        else:
            return "会员增长乏力"

    @classmethod
    def _get_completion_assessment(cls, ratio: float) -> str:
        if ratio >= 90:
            return "信息完善度优秀"
        elif ratio >= 70:
            return "信息完善度良好"
        elif ratio >= 50:
            return "信息完善度一般"
        else:
            return "信息完善度有待提升"

    @classmethod
    def _get_activity_assessment(cls, ratio: float) -> str:
        if ratio >= 70:
            return "会员活跃度很高"
        elif ratio >= 50:
            return "会员活跃度较好"
        elif ratio >= 30:
            return "会员活跃度一般"
        else:
            return "会员活跃度偏低"

    @classmethod
    def _get_growth_analysis(cls, ratio: float) -> str:
        if ratio >= 15:
            return "新增会员占比较高，用户增长迅速"
        elif ratio >= 8:
            return "新增会员占比适中，增长稳定"
        else:
            return "新增会员占比偏低，需要加强获客"

    @classmethod
    def _get_growth_suggestion(cls, ratio: float) -> str:
        if ratio >= 15:
            return "继续保持现有获客策略，同时关注新用户留存"
        elif ratio >= 8:
            return "适度加大营销投入，提升获客效率"
        else:
            return "重点优化获客渠道，制定针对性营销策略"

    @classmethod
    def _get_quality_analysis(cls, info_ratio: float, consume_ratio: float) -> str:
        if info_ratio >= 80 and consume_ratio >= 60:
            return "会员质量优秀，信息完善且活跃度高"
        elif info_ratio >= 60 and consume_ratio >= 40:
            return "会员质量良好，有一定提升空间"
        else:
            return "会员质量有待提升，需要加强运营"

    @classmethod
    def _get_quality_suggestion(cls, ratio: float) -> str:
        if ratio >= 80:
            return "继续维护高质量会员群体"
        elif ratio >= 60:
            return "通过激励措施提升会员信息完善度"
        else:
            return "建立会员信息完善激励机制"

    @classmethod
    def _get_new_member_analysis(cls, ratio: float) -> str:
        if ratio >= 20:
            return "新会员增长迅猛，获客效果显著"
        elif ratio >= 10:
            return "新会员增长稳定，获客渠道有效"
        else:
            return "新会员增长缓慢，需要优化获客策略"

    @classmethod
    def _get_retention_suggestion(cls, ratio: float) -> str:
        if ratio >= 20:
            return "重点关注新会员留存和转化"
        elif ratio >= 10:
            return "平衡新会员获取和老会员维护"
        else:
            return "加强新会员获取和激活"

    @classmethod
    def _get_info_complete_analysis(cls, ratio: float) -> str:
        if ratio >= 90:
            return "信息完善度表现优异"
        elif ratio >= 70:
            return "信息完善度表现良好"
        else:
            return "信息完善度需要改善"

    @classmethod
    def _get_info_improvement_suggestion(cls, ratio: float) -> str:
        if ratio >= 90:
            return "维持当前信息收集策略"
        elif ratio >= 70:
            return "通过小幅激励提升完善度"
        else:
            return "建立强激励机制促进信息完善"

    @classmethod
    def _get_consume_analysis(cls, ratio: float) -> str:
        if ratio >= 70:
            return "消费参与度表现优秀"
        elif ratio >= 50:
            return "消费参与度表现良好"
        else:
            return "消费参与度有待提升"

    @classmethod
    def _get_consume_improvement_suggestion(cls, ratio: float) -> str:
        if ratio >= 70:
            return "继续优化消费体验"
        elif ratio >= 50:
            return "通过促销活动提升消费意愿"
        else:
            return "制定消费激活策略"

    @classmethod
    def _get_charge_analysis(cls, ratio: float) -> str:
        if ratio >= 50:
            return "充值参与度表现优秀"
        elif ratio >= 30:
            return "充值参与度表现良好"
        else:
            return "充值参与度偏低"

    @classmethod
    def _get_charge_strategy_suggestion(cls, ratio: float) -> str:
        if ratio >= 50:
            return "维持现有充值策略"
        elif ratio >= 30:
            return "适度优化充值优惠政策"
        else:
            return "重新设计充值激励方案"

    @classmethod
    def _get_structure_analysis(cls, new_ratio: float, consume_ratio: float) -> str:
        if new_ratio >= 15 and consume_ratio >= 60:
            return "呈现健康的增长型结构"
        elif new_ratio >= 10 and consume_ratio >= 40:
            return "呈现稳定的发展型结构"
        else:
            return "结构有待优化"

    @classmethod
    def _get_structure_optimization_suggestion(cls, consume_ratio: float) -> str:
        if consume_ratio >= 60:
            return "继续保持良好的会员结构"
        elif consume_ratio >= 40:
            return "适度提升会员活跃度"
        else:
            return "重点改善会员活跃度"

    @classmethod
    def _get_retention_analysis(cls, new_ratio: float) -> str:
        if new_ratio >= 20:
            return "新增会员较多，需关注留存"
        elif new_ratio >= 10:
            return "新增会员适中，留存稳定"
        else:
            return "新增会员偏少，留存压力小"

    @classmethod
    def _get_retention_strategy_suggestion(cls, new_ratio: float) -> str:
        if new_ratio >= 20:
            return "建立新会员专项留存计划"
        elif new_ratio >= 10:
            return "平衡新老会员运营策略"
        else:
            return "重点提升获客能力"

    @classmethod
    def _get_value_analysis(cls, consume_ratio: float, charge_ratio: float) -> str:
        if consume_ratio >= 60 and charge_ratio >= 40:
            return "会员价值较高，消费和充值都活跃"
        elif consume_ratio >= 40 and charge_ratio >= 20:
            return "会员价值中等，有提升空间"
        else:
            return "会员价值偏低，需要激活"

    @classmethod
    def _get_value_enhancement_suggestion(cls, consume_ratio: float) -> str:
        if consume_ratio >= 60:
            return "通过精准营销提升客单价"
        elif consume_ratio >= 40:
            return "通过促销活动提升消费频次"
        else:
            return "通过激活策略提升消费意愿"

    @classmethod
    def _get_operation_analysis(cls, info_ratio: float, consume_ratio: float) -> str:
        if info_ratio >= 80 and consume_ratio >= 60:
            return "运营效果优秀"
        elif info_ratio >= 60 and consume_ratio >= 40:
            return "运营效果良好"
        else:
            return "运营效果有待提升"

    @classmethod
    def _get_operation_optimization_suggestion(cls, consume_ratio: float) -> str:
        if consume_ratio >= 60:
            return "继续优化精细化运营"
        elif consume_ratio >= 40:
            return "加强个性化营销"
        else:
            return "重新制定运营策略"

    @classmethod
    def _get_potential_analysis(cls, new_ratio: float, consume_ratio: float) -> str:
        if new_ratio >= 15 and consume_ratio >= 50:
            return "发展潜力巨大"
        elif new_ratio >= 10 and consume_ratio >= 30:
            return "发展潜力良好"
        else:
            return "发展潜力有限"

    @classmethod
    def _get_potential_development_suggestion(cls, new_ratio: float) -> str:
        if new_ratio >= 15:
            return "加大投入扩大规模"
        elif new_ratio >= 10:
            return "稳步推进发展计划"
        else:
            return "重新评估发展策略"

    @classmethod
    def _get_management_analysis(cls, info_ratio: float) -> str:
        if info_ratio >= 80:
            return "管理水平较高"
        elif info_ratio >= 60:
            return "管理水平中等"
        else:
            return "管理水平有待提升"

    @classmethod
    def _get_management_improvement_suggestion(cls, info_ratio: float) -> str:
        if info_ratio >= 80:
            return "继续保持精细化管理"
        elif info_ratio >= 60:
            return "加强数据化管理"
        else:
            return "建立系统化管理体系"

    @classmethod
    def generate_member_revenue_analysis(cls, data: Dict[str, Any]) -> str:
        """
        生成会员收入分析文本

        Args:
            data: 收入数据字典

        Returns:
            str: 分析文本
        """
        try:
            # 提取数据并处理字符串格式的金额
            total_actual_amount_str = data.get("total_actual_amount", "0")
            prepay_actual_amount_str = data.get("prepay_actual_amount", "0")
            first_consume_amount_str = data.get("first_consume_amount", "0")
            repeat_consume_amount_str = data.get("repeat_consume_amount", "0")

            # 移除金额字符串中的逗号和货币符号，转换为数字
            total_actual_amount = float(total_actual_amount_str.replace(",", "").replace("¥", ""))
            prepay_actual_amount = float(prepay_actual_amount_str.replace(",", "").replace("¥", ""))
            first_consume_amount = float(first_consume_amount_str.replace(",", "").replace("¥", ""))
            repeat_consume_amount = float(repeat_consume_amount_str.replace(",", "").replace("¥", ""))

            avg_consume_amount = float(data.get("avg_consume_amount", 0))
            consume_frequency = float(data.get("consume_frequency", 0))
            consume_users = int(data.get("consume_users", 0))

            # 计算比例和人均消费
            prepay_ratio = (prepay_actual_amount / total_actual_amount * 100) if total_actual_amount > 0 else 0
            cash_ratio = 100 - prepay_ratio
            repeat_ratio = (repeat_consume_amount / total_actual_amount * 100) if total_actual_amount > 0 else 0
            per_capita_consumption = total_actual_amount / consume_users if consume_users > 0 else 0

            # 生成分析内容
            analysis_parts = []

            # 选择3-4个模板进行分析
            selected_templates = random.sample(cls.MEMBER_REVENUE_ANALYSIS_TEMPLATES, min(4, len(cls.MEMBER_REVENUE_ANALYSIS_TEMPLATES)))

            for template in selected_templates:
                try:
                    analysis_text = template.format(
                        total_actual_amount=f"{total_actual_amount:,.0f}",
                        prepay_actual_amount=f"{prepay_actual_amount:,.0f}",
                        first_consume_amount=f"{first_consume_amount:,.0f}",
                        repeat_consume_amount=f"{repeat_consume_amount:,.0f}",
                        avg_consume_amount=f"{avg_consume_amount:.0f}",
                        consume_frequency=f"{consume_frequency:.1f}",
                        consume_users=consume_users,
                        prepay_ratio=prepay_ratio,
                        cash_ratio=cash_ratio,
                        repeat_ratio=repeat_ratio,
                        per_capita_consumption=per_capita_consumption,
                        revenue_trend_analysis=cls._get_revenue_trend_analysis(total_actual_amount),
                        payment_structure_analysis=cls._get_payment_structure_analysis(prepay_ratio),
                        loyalty_analysis=cls._get_loyalty_analysis(repeat_ratio),
                        consumption_efficiency_analysis=cls._get_consumption_efficiency_analysis(per_capita_consumption),
                        revenue_structure_analysis=cls._get_revenue_structure_analysis(prepay_ratio, repeat_ratio),
                        revenue_optimization_suggestion=cls._get_revenue_optimization_suggestion(prepay_ratio),
                        frequency_analysis=cls._get_frequency_analysis(consume_frequency),
                        frequency_improvement_suggestion=cls._get_frequency_improvement_suggestion(consume_frequency),
                        amount_analysis=cls._get_amount_analysis(avg_consume_amount),
                        amount_enhancement_suggestion=cls._get_amount_enhancement_suggestion(avg_consume_amount),
                        prepay_usage_analysis=cls._get_prepay_usage_analysis(prepay_ratio),
                        prepay_strategy_suggestion=cls._get_prepay_strategy_suggestion(prepay_ratio),
                        repeat_consumption_analysis=cls._get_repeat_consumption_analysis(repeat_ratio),
                        repeat_consumption_strategy=cls._get_repeat_consumption_strategy(repeat_ratio),
                        consumption_conversion_analysis=cls._get_consumption_conversion_analysis(consume_users),
                        conversion_improvement_suggestion=cls._get_conversion_improvement_suggestion(consume_users),
                        revenue_growth_analysis=cls._get_revenue_growth_analysis(total_actual_amount),
                        growth_strategy_suggestion=cls._get_growth_strategy_suggestion(total_actual_amount),
                        consumption_behavior_analysis=cls._get_consumption_behavior_analysis(consume_frequency),
                        behavior_optimization_suggestion=cls._get_behavior_optimization_suggestion(consume_frequency),
                        revenue_quality_analysis=cls._get_revenue_quality_analysis(repeat_ratio),
                        quality_improvement_suggestion=cls._get_quality_improvement_suggestion(repeat_ratio),
                        consumption_value_analysis=cls._get_consumption_value_analysis(per_capita_consumption),
                        value_maximization_suggestion=cls._get_value_maximization_suggestion(per_capita_consumption),
                        revenue_stability_analysis=cls._get_revenue_stability_analysis(prepay_ratio),
                        stability_enhancement_suggestion=cls._get_stability_enhancement_suggestion(prepay_ratio)
                    )
                    analysis_parts.append(analysis_text)
                except (KeyError, ValueError) as e:
                    # 如果模板格式化失败，跳过这个模板
                    continue

            return " ".join(analysis_parts) if analysis_parts else "收入分析暂时无法生成，请检查数据完整性。"

        except Exception as e:
            return f"收入分析生成失败: {str(e)}"

    @classmethod
    def format_analysis_as_numbered_list(cls, analysis_text: str) -> str:
        """
        将分析文本格式化为编号列表格式

        Args:
            analysis_text: 原始分析文本

        Returns:
            str: 格式化后的编号列表文本
        """
        if not analysis_text or analysis_text.startswith("数据分析") or analysis_text.startswith("收入分析"):
            return analysis_text

        # 按句号分割文本
        sentences = [s.strip() for s in analysis_text.split("。") if s.strip()]

        # 如果句子数量少于3个，直接返回原文本
        if len(sentences) < 3:
            return analysis_text

        # 选择前3个最重要的句子，或者平均分配
        if len(sentences) >= 3:
            # 取前3个句子
            selected_sentences = sentences[:3]
        else:
            selected_sentences = sentences

        # 格式化为编号列表
        formatted_parts = []
        for i, sentence in enumerate(selected_sentences, 1):
            # 确保句子以句号结尾
            if not sentence.endswith("。"):
                sentence += "。"
            formatted_parts.append(f"{i}、{sentence}")

        return "\n".join(formatted_parts)

    @classmethod
    def generate_formatted_member_data_analysis(cls, data: Dict[str, Any]) -> str:
        """
        生成格式化的会员数据分析（编号列表格式）

        Args:
            data: 会员数据字典

        Returns:
            str: 格式化的分析文本
        """
        analysis_text = cls.generate_member_data_analysis(data)
        return cls.format_analysis_as_numbered_list(analysis_text)

    @classmethod
    def generate_formatted_member_revenue_analysis(cls, data: Dict[str, Any]) -> str:
        """
        生成格式化的会员收入分析（编号列表格式）

        Args:
            data: 收入数据字典

        Returns:
            str: 格式化的分析文本
        """
        analysis_text = cls.generate_member_revenue_analysis(data)
        return cls.format_analysis_as_numbered_list(analysis_text)

    # 收入分析辅助方法（关键方法）
    @classmethod
    def _get_revenue_trend_analysis(cls, amount: float) -> str:
        if amount >= 1000000:
            return "收入规模较大，表现优异"
        elif amount >= 500000:
            return "收入规模中等，发展良好"
        else:
            return "收入规模偏小，有增长空间"

    @classmethod
    def _get_payment_structure_analysis(cls, prepay_ratio: float) -> str:
        if prepay_ratio >= 60:
            return "储值消费占主导，用户粘性强"
        elif prepay_ratio >= 40:
            return "储值与现金消费均衡"
        else:
            return "现金消费为主，储值推广有空间"

    @classmethod
    def _get_loyalty_analysis(cls, repeat_ratio: float) -> str:
        if repeat_ratio >= 70:
            return "用户忠诚度很高"
        elif repeat_ratio >= 50:
            return "用户忠诚度良好"
        else:
            return "用户忠诚度有待提升"

    @classmethod
    def _get_consumption_efficiency_analysis(cls, per_capita: float) -> str:
        if per_capita >= 200:
            return "消费效率优秀"
        elif per_capita >= 100:
            return "消费效率良好"
        else:
            return "消费效率有待提升"

    @classmethod
    def _get_revenue_structure_analysis(cls, prepay_ratio: float, repeat_ratio: float) -> str:
        if prepay_ratio >= 50 and repeat_ratio >= 60:
            return "收入结构健康，储值和复购都表现良好"
        elif prepay_ratio >= 30 and repeat_ratio >= 40:
            return "收入结构基本合理，有优化空间"
        else:
            return "收入结构需要调整"

    @classmethod
    def _get_revenue_optimization_suggestion(cls, prepay_ratio: float) -> str:
        if prepay_ratio >= 60:
            return "继续维护储值用户，提升服务质量"
        elif prepay_ratio >= 40:
            return "适度推广储值优惠，提升储值比例"
        else:
            return "重点推广储值业务，增强用户粘性"

    @classmethod
    def _get_frequency_analysis(cls, frequency: float) -> str:
        if frequency >= 4:
            return "消费频次很高，用户活跃"
        elif frequency >= 2:
            return "消费频次适中"
        else:
            return "消费频次偏低"

    @classmethod
    def _get_frequency_improvement_suggestion(cls, frequency: float) -> str:
        if frequency >= 4:
            return "维持高频消费，提升服务体验"
        elif frequency >= 2:
            return "通过活动促进消费频次提升"
        else:
            return "制定消费激励计划，提升活跃度"

    @classmethod
    def _get_amount_analysis(cls, amount: float) -> str:
        if amount >= 200:
            return "表现优秀，客单价较高"
        elif amount >= 100:
            return "表现良好，有提升空间"
        else:
            return "偏低，需要提升客单价"

    @classmethod
    def _get_amount_enhancement_suggestion(cls, amount: float) -> str:
        if amount >= 200:
            return "通过增值服务进一步提升客单价"
        elif amount >= 100:
            return "通过套餐组合提升客单价"
        else:
            return "重新设计产品定价策略"

    # 其他辅助方法使用默认实现
    @classmethod
    def _get_prepay_usage_analysis(cls, ratio: float) -> str:
        return f"储值使用占比{ratio:.1f}%，" + ("表现优秀" if ratio >= 50 else "有提升空间")

    @classmethod
    def _get_prepay_strategy_suggestion(cls, ratio: float) -> str:
        return "继续优化储值策略" if ratio >= 50 else "加强储值推广"

    @classmethod
    def _get_repeat_consumption_analysis(cls, ratio: float) -> str:
        return f"重复消费占比{ratio:.1f}%，" + ("用户粘性强" if ratio >= 60 else "需要提升")

    @classmethod
    def _get_repeat_consumption_strategy(cls, ratio: float) -> str:
        return "维护忠诚用户群体" if ratio >= 60 else "建立用户留存机制"

    @classmethod
    def _get_consumption_conversion_analysis(cls, users: int) -> str:
        return f"消费用户{users}人，" + ("转化效果好" if users >= 1000 else "转化有空间")

    @classmethod
    def _get_conversion_improvement_suggestion(cls, users: int) -> str:
        return "优化转化流程" if users >= 1000 else "加强转化引导"

    @classmethod
    def _get_revenue_growth_analysis(cls, amount: float) -> str:
        return "增长潜力大" if amount >= 500000 else "需要加强增长动力"

    @classmethod
    def _get_growth_strategy_suggestion(cls, amount: float) -> str:
        return "扩大市场份额" if amount >= 500000 else "重点提升收入规模"

    @classmethod
    def _get_consumption_behavior_analysis(cls, frequency: float) -> str:
        return "消费行为活跃" if frequency >= 3 else "消费行为需要激活"

    @classmethod
    def _get_behavior_optimization_suggestion(cls, frequency: float) -> str:
        return "优化消费体验" if frequency >= 3 else "建立消费激励机制"

    @classmethod
    def _get_revenue_quality_analysis(cls, repeat_ratio: float) -> str:
        return "收入质量高" if repeat_ratio >= 60 else "收入质量有待提升"

    @classmethod
    def _get_quality_improvement_suggestion(cls, repeat_ratio: float) -> str:
        return "维持高质量收入" if repeat_ratio >= 60 else "提升收入质量"

    @classmethod
    def _get_consumption_value_analysis(cls, per_capita: float) -> str:
        return "消费价值高" if per_capita >= 150 else "消费价值有提升空间"

    @classmethod
    def _get_value_maximization_suggestion(cls, per_capita: float) -> str:
        return "继续价值挖掘" if per_capita >= 150 else "提升消费价值"

    @classmethod
    def _get_revenue_stability_analysis(cls, prepay_ratio: float) -> str:
        return "收入稳定性好" if prepay_ratio >= 50 else "收入稳定性一般"

    @classmethod
    def _get_stability_enhancement_suggestion(cls, prepay_ratio: float) -> str:
        return "维持稳定收入结构" if prepay_ratio >= 50 else "提升收入稳定性"