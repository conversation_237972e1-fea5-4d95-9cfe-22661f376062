from fastapi import APIRouter, HTTPException, Depends
from typing import Optional, Dict, Any, List
import logging
from datetime import datetime, timedelta
import asyncio
import time

from core.database import db
from core.models import QueryParams, MemberChargeData, FieldDataModel, ResponseModel
from constant import MEMBER_CHARGE_MODULE
from api.query.MemberChargeSql import MemberChargeSqlQueries, MemberChargeCalculator

logger = logging.getLogger(__name__)

router = APIRouter()

class MemberChargeService:
    """会员充值数据服务"""
    
    def __init__(self):
        self.module_config = MEMBER_CHARGE_MODULE
        self.sql_queries = MemberChargeSqlQueries()
        self.calculator = MemberChargeCalculator()
    
    def _calculate_time_ranges(self, query_params: QueryParams) -> Dict[str, Any]:
        """计算时间范围，包括当前期间、环比期间、同比期间"""
        start_date = datetime.strptime(query_params.start_date, "%Y-%m-%d")
        end_date = datetime.strptime(query_params.end_date, "%Y-%m-%d")
        
        # 计算期间长度
        period_days = (end_date - start_date).days + 1
        
        # 环比期间（上一个相同长度的期间）
        chain_end_date = start_date - timedelta(days=1)
        chain_start_date = chain_end_date - timedelta(days=period_days - 1)
        
        # 同比期间（去年同期）
        year_over_year_start = start_date.replace(year=start_date.year - 1)
        year_over_year_end = end_date.replace(year=end_date.year - 1)
        
        return {
            "current": {
                "start": start_date.strftime("%Y%m%d"),
                "end": end_date.strftime("%Y%m%d"),
                "label": "本期"
            },
            "chain": {
                "start": chain_start_date.strftime("%Y%m%d"),
                "end": chain_end_date.strftime("%Y%m%d"),
                "label": "上期"
            },
            "year_over_year": {
                "start": year_over_year_start.strftime("%Y%m%d"),
                "end": year_over_year_end.strftime("%Y%m%d"),
                "label": "去年同期"
            }
        }
    
    async def _fetch_dwoutput_charge_base_data(self, start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> Dict[str, Any]:
        """获取dwoutput数据库的充值基础数据 - 独立查询版本"""
        try:
            logger.info(f"开始查询dwoutput充值基础数据 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            start_time = time.time()
            
            result = {}
            sid_condition = f"AND sid = '{sid}'" if sid else ""
            
            # 1. 查询充值笔数
            charge_pv_sql = f"""
            SELECT {MemberChargeSqlQueries.get_dwoutput_total_charge_pv_sql(start_date, end_date, bid, sid)}
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN {start_date} AND {end_date}
              AND bid = {bid}
              {sid_condition}
            """
            charge_pv_result = await db.execute_dwoutput_one(charge_pv_sql)
            result['total_charge_pv'] = charge_pv_result.get('total_charge_pv', 0) if charge_pv_result else 0
            
            # 2. 查询实收充值金额
            charge_cash_sql = f"""
            SELECT {MemberChargeSqlQueries.get_dwoutput_total_charge_cash_sql(start_date, end_date, bid, sid)}
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN {start_date} AND {end_date}
              AND bid = {bid}
              {sid_condition}
            """
            charge_cash_result = await db.execute_dwoutput_one(charge_cash_sql)
            result['total_charge_cash'] = charge_cash_result.get('total_charge_cash', 0) if charge_cash_result else 0
            
            # 3. 查询期间充值金额
            charge_amount_sql = f"""
            SELECT {MemberChargeSqlQueries.get_dwoutput_total_charge_amount_sql(start_date, end_date, bid, sid)}
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN {start_date} AND {end_date}
              AND bid = {bid}
              {sid_condition}
            """
            charge_amount_result = await db.execute_dwoutput_one(charge_amount_sql)
            result['total_charge_amount'] = charge_amount_result.get('total_charge_amount', 0) if charge_amount_result else 0
            
            # 4. 查询期间充值赠送金额
            charge_present_sql = f"""
            SELECT {MemberChargeSqlQueries.get_dwoutput_total_charge_present_sql(start_date, end_date, bid, sid)}
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN {start_date} AND {end_date}
              AND bid = {bid}
              {sid_condition}
            """
            charge_present_result = await db.execute_dwoutput_one(charge_present_sql)
            result['total_charge_present'] = charge_present_result.get('total_charge_present', 0) if charge_present_result else 0
            
            # 5. 查询期末储值沉淀金额
            charge_unused_sql = f"""
            SELECT {MemberChargeSqlQueries.get_dwoutput_charge_amount_unused_sql(start_date, end_date, bid, sid)}
            """
            charge_unused_result = await db.execute_dwoutput_one(charge_unused_sql)
            result['total_charge_amount_unused'] = charge_unused_result.get('total_charge_amount_unused', 0) if charge_unused_result else 0
            
            query_time = time.time() - start_time
            logger.info(f"dwoutput充值基础数据查询完成，耗时: {query_time:.3f}秒")
            logger.info(f"充值基础数据结果: {result}")
            
            return result
            
        except Exception as e:
            logger.error(f"获取dwoutput充值基础数据失败: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"dwoutput充值基础数据查询失败: {str(e)}")
    
    async def _fetch_dwoutput_consume_detail_data(self, start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> Dict[str, Any]:
        """获取dwoutput数据库的储值消耗详情数据 - 新逻辑版本"""
        try:
            logger.info(f"开始查询dwoutput储值消耗详情 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            start_time = time.time()
            
            consume_detail_sql = f"""
            SELECT {MemberChargeSqlQueries.get_dwoutput_total_consume_prepay_used_sql(start_date, end_date, bid, sid)}
            """
            logger.info(f"储值消耗详情查询SQL: {consume_detail_sql}")
            result = await db.execute_dwoutput_one(consume_detail_sql)
            
            query_time = time.time() - start_time
            logger.info(f"dwoutput储值消耗详情查询完成，耗时: {query_time:.3f}秒")
            logger.info(f"储值消耗详情结果: {result}")
            
            return result or {}
            
        except Exception as e:
            logger.error(f"获取dwoutput储值消耗详情失败: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"dwoutput储值消耗详情查询失败: {str(e)}")

    async def _fetch_member_charge_data(self, start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> Dict[str, Any]:
        """获取会员充值数据 - 重构后的版本"""
        try:
            logger.info(f"开始获取会员充值数据 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            total_start_time = time.time()
            
            # 并行查询两个数据源
            charge_base_data, consume_detail_data = await asyncio.gather(
                self._fetch_dwoutput_charge_base_data(start_date, end_date, bid, sid),
                self._fetch_dwoutput_consume_detail_data(start_date, end_date, bid, sid)
            )
            
            if not charge_base_data:
                logger.warning(f"充值基础数据查询异常: bid={bid}, sid={sid}")
                return {}
            
            # 使用重构后的独立计算函数合并数据
            result = MemberChargeCalculator.merge_charge_data(charge_base_data, consume_detail_data)
            
            # 使用重构后的独立函数处理金额字段
            money_fields = [
                'total_charge_cash', 'total_consume_prepay_used', 
                'total_charge_amount', 'total_charge_present', 'total_charge_amount_unused'
            ]
            result = MemberChargeCalculator.process_money_fields(result, money_fields)
            
            total_time = time.time() - total_start_time
            logger.info(f"会员充值数据获取完成，总耗时: {total_time:.3f}秒，数据字段数: {len(result)}")
            
            return result
            
        except Exception as e:
            logger.error(f"获取会员充值数据失败: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"数据查询失败: {str(e)}")
    
    def _calculate_change_rate(self, current: float, previous: float) -> str:
        """计算变化率"""
        if previous == 0:
            return "0%" if current == 0 else "100%"
        
        rate = ((current - previous) / previous) * 100
        return f"{rate:+.2f}%"
    
    def _map_db_result_to_response(self, current_data: Dict[str, Any], 
                                  chain_data: Dict[str, Any], 
                                  year_data: Dict[str, Any]) -> MemberChargeData:
        """将数据库结果映射到响应模型"""
        
        # 映射字段名到数据库字段 - 基于 MEMBER_CHARGE_MODULE 常量配置
        field_mapping = {
            "charge_count": ("total_charge_pv", "笔"),
            "charge_amount": ("total_charge_cash", "元"),
            "period_charge_amount": ("total_charge_amount", "元"),
            "period_charge_present": ("total_charge_present", "元"),
            "period_charge_amount_unused": ("total_charge_amount_unused", "元"),
            "consume_prepay_amount": ("total_consume_prepay_used", "元"),
            "retention_rate": ("prepay_retention_rate", "%")
        }
        
        result = MemberChargeData()
        
        for field_name, (db_field, unit) in field_mapping.items():
            current_value = current_data.get(db_field, 0) or 0
            chain_value = chain_data.get(db_field, 0) or 0
            year_value = year_data.get(db_field, 0) or 0

            # 处理数值类型
            if unit == "元":
                current_value = float(current_value) if current_value else 0.0
                chain_value = float(chain_value) if chain_value else 0.0
                year_value = float(year_value) if year_value else 0.0
            elif unit == "%":
                current_value = float(current_value) if current_value else 0.0
                chain_value = float(chain_value) if chain_value else 0.0
                year_value = float(year_value) if year_value else 0.0
            else:
                current_value = int(current_value) if current_value else 0
                chain_value = int(chain_value) if chain_value else 0
                year_value = int(year_value) if year_value else 0

            field_data = FieldDataModel(
                value=current_value,
                unit=unit,
                chain_comparison=[chain_value],
                chain_change_rate=[self._calculate_change_rate(current_value, chain_value)],
                chain_labels=["上期"],
                year_over_year=year_value,
                year_over_year_rate=self._calculate_change_rate(current_value, year_value)
            )

            setattr(result, field_name, field_data)
        
        return result
    
    async def get_member_charge_data(self, query_params: QueryParams) -> MemberChargeData:
        """获取会员充值数据"""
        try:
            logger.info(f"收到会员充值数据查询请求 - bid: {query_params.bid}, sid: {query_params.sid}, 日期: {query_params.start_date}~{query_params.end_date}")
            
            # 计算时间范围
            time_ranges = self._calculate_time_ranges(query_params)
            logger.debug(f"时间范围计算完成: {time_ranges}")
            
            # 并行获取当前期间、环比期间、同比期间的数据
            current_data, chain_data, year_data = await asyncio.gather(
                self._fetch_member_charge_data(
                    time_ranges["current"]["start"],
                    time_ranges["current"]["end"],
                    query_params.bid,
                    query_params.sid
                ),
                self._fetch_member_charge_data(
                    time_ranges["chain"]["start"],
                    time_ranges["chain"]["end"],
                    query_params.bid,
                    query_params.sid
                ),
                self._fetch_member_charge_data(
                    time_ranges["year_over_year"]["start"],
                    time_ranges["year_over_year"]["end"],
                    query_params.bid,
                    query_params.sid
                )
            )
            
            # 映射数据到响应模型
            result = self._map_db_result_to_response(current_data, chain_data, year_data)
            
            logger.info(f"会员充值数据查询成功完成 - bid: {query_params.bid}, sid: {query_params.sid}")
            return result
            
        except Exception as e:
            logger.error(f"获取会员充值数据失败: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"获取会员充值数据失败: {str(e)}")


# 创建服务实例
member_charge_service = MemberChargeService()


@router.post("/member-charge", response_model=ResponseModel)
async def get_member_charge_data(query_params: QueryParams):
    """获取会员充值数据"""
    try:
        logger.info(f"API接收到会员充值数据请求: {query_params}")
        data = await member_charge_service.get_member_charge_data(query_params)
        logger.info("会员充值数据API调用成功")
        return ResponseModel(
            code=200,
            message="获取会员充值数据成功",
            data=data
        )
    except HTTPException as e:
        logger.error(f"HTTP异常: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"获取会员充值数据异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
